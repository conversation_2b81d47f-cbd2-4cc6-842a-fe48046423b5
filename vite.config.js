import { sentryVitePlugin } from "@sentry/vite-plugin";
import { defineConfig } from "vite";
import laravel, { refreshPaths } from "laravel-vite-plugin";
import react from "@vitejs/plugin-react";

export default defineConfig({
    plugins: [laravel({
        input: "resources/js/app.jsx",
        ssr: "resources/js/ssr.jsx",
        refresh: [...refreshPaths, "app/Livewire/**", "app/Filament/**"],
    }), react(), sentryVitePlugin({
        org: "ticketgol",
        project: "react-frontend"
    })],

    build: {
        sourcemap: true
    },
    define: {
      __TRANSLATIONS_VERSION__: JSON.stringify(new Date().toISOString()),
    }
});
