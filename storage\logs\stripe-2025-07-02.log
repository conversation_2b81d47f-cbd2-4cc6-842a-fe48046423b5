[2025-07-02 17:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:40:01] local.INFO: <PERSON>ron job executed {"time":"2025-07-02 17:40:01"} 
[2025-07-02 17:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:40:02] local.INFO: Cron job executed {"time":"2025-07-02 17:40:02"} 
[2025-07-02 17:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:41:01] local.INFO: Cron job executed {"time":"2025-07-02 17:41:01"} 
[2025-07-02 17:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:41:03] local.INFO: Cron job executed {"time":"2025-07-02 17:41:03"} 
[2025-07-02 17:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:42:01] local.INFO: Cron job executed {"time":"2025-07-02 17:42:01"} 
[2025-07-02 17:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:42:02] local.INFO: Cron job executed {"time":"2025-07-02 17:42:02"} 
[2025-07-02 17:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:43:01] local.INFO: Cron job executed {"time":"2025-07-02 17:43:01"} 
[2025-07-02 17:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:43:02] local.INFO: Cron job executed {"time":"2025-07-02 17:43:02"} 
[2025-07-02 17:44:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:44:02] local.INFO: Cron job executed {"time":"2025-07-02 17:44:02"} 
[2025-07-02 17:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:44:03] local.INFO: Cron job executed {"time":"2025-07-02 17:44:03"} 
[2025-07-02 17:45:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:45:02] local.INFO: Cron job executed {"time":"2025-07-02 17:45:02"} 
[2025-07-02 17:45:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:45:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:45:03] local.INFO: Cron job executed {"time":"2025-07-02 17:45:03"} 
[2025-07-02 17:46:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:46:02] local.INFO: Cron job executed {"time":"2025-07-02 17:46:02"} 
[2025-07-02 17:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:46:03] local.INFO: Cron job executed {"time":"2025-07-02 17:46:03"} 
[2025-07-02 17:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:47:01] local.INFO: Cron job executed {"time":"2025-07-02 17:47:01"} 
[2025-07-02 17:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:47:02] local.INFO: Cron job executed {"time":"2025-07-02 17:47:02"} 
[2025-07-02 17:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:48:01] local.INFO: Cron job executed {"time":"2025-07-02 17:48:01"} 
[2025-07-02 17:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:48:02] local.INFO: Cron job executed {"time":"2025-07-02 17:48:02"} 
[2025-07-02 17:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:49:01] local.INFO: Cron job executed {"time":"2025-07-02 17:49:01"} 
[2025-07-02 17:49:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:49:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:49:03] local.INFO: Cron job executed {"time":"2025-07-02 17:49:03"} 
[2025-07-02 17:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:50:01] local.INFO: Cron job executed {"time":"2025-07-02 17:50:01"} 
[2025-07-02 17:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:50:02] local.INFO: Cron job executed {"time":"2025-07-02 17:50:02"} 
[2025-07-02 17:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:51:01] local.INFO: Cron job executed {"time":"2025-07-02 17:51:01"} 
[2025-07-02 17:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:51:02] local.INFO: Cron job executed {"time":"2025-07-02 17:51:02"} 
[2025-07-02 17:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:52:01] local.INFO: Cron job executed {"time":"2025-07-02 17:52:01"} 
[2025-07-02 17:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:52:02] local.INFO: Cron job executed {"time":"2025-07-02 17:52:02"} 
[2025-07-02 17:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:53:01] local.INFO: Cron job executed {"time":"2025-07-02 17:53:01"} 
[2025-07-02 17:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:53:02] local.INFO: Cron job executed {"time":"2025-07-02 17:53:02"} 
[2025-07-02 17:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:54:01] local.INFO: Cron job executed {"time":"2025-07-02 17:54:01"} 
[2025-07-02 17:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:54:02] local.INFO: Cron job executed {"time":"2025-07-02 17:54:02"} 
[2025-07-02 17:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:55:01] local.INFO: Cron job executed {"time":"2025-07-02 17:55:01"} 
[2025-07-02 17:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:55:02] local.INFO: Cron job executed {"time":"2025-07-02 17:55:02"} 
[2025-07-02 17:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:56:01] local.INFO: Cron job executed {"time":"2025-07-02 17:56:01"} 
[2025-07-02 17:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:56:02] local.INFO: Cron job executed {"time":"2025-07-02 17:56:02"} 
[2025-07-02 17:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:57:01] local.INFO: Cron job executed {"time":"2025-07-02 17:57:01"} 
[2025-07-02 17:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:57:02] local.INFO: Cron job executed {"time":"2025-07-02 17:57:02"} 
[2025-07-02 17:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:58:01] local.INFO: Cron job executed {"time":"2025-07-02 17:58:01"} 
[2025-07-02 17:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:58:02] local.INFO: Cron job executed {"time":"2025-07-02 17:58:02"} 
[2025-07-02 17:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:59:01] local.INFO: Cron job executed {"time":"2025-07-02 17:59:01"} 
[2025-07-02 17:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:59:02] local.INFO: Cron job executed {"time":"2025-07-02 17:59:02"} 
[2025-07-02 18:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:00:01] local.INFO: Cron job executed {"time":"2025-07-02 18:00:01"} 
[2025-07-02 18:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:00:02] local.INFO: Cron job executed {"time":"2025-07-02 18:00:02"} 
[2025-07-02 18:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:01:01] local.INFO: Cron job executed {"time":"2025-07-02 18:01:01"} 
[2025-07-02 18:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:01:02] local.INFO: Cron job executed {"time":"2025-07-02 18:01:02"} 
[2025-07-02 18:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:02:01] local.INFO: Cron job executed {"time":"2025-07-02 18:02:01"} 
[2025-07-02 18:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:02:02] local.INFO: Cron job executed {"time":"2025-07-02 18:02:02"} 
[2025-07-02 18:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:03:01] local.INFO: Cron job executed {"time":"2025-07-02 18:03:01"} 
[2025-07-02 18:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:03:02] local.INFO: Cron job executed {"time":"2025-07-02 18:03:02"} 
[2025-07-02 18:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:04:01] local.INFO: Cron job executed {"time":"2025-07-02 18:04:01"} 
[2025-07-02 18:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:04:02] local.INFO: Cron job executed {"time":"2025-07-02 18:04:02"} 
[2025-07-02 18:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:05:01] local.INFO: Cron job executed {"time":"2025-07-02 18:05:01"} 
[2025-07-02 18:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:05:02] local.INFO: Cron job executed {"time":"2025-07-02 18:05:02"} 
[2025-07-02 18:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:06:01] local.INFO: Cron job executed {"time":"2025-07-02 18:06:01"} 
[2025-07-02 18:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:06:02] local.INFO: Cron job executed {"time":"2025-07-02 18:06:02"} 
[2025-07-02 18:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:07:01] local.INFO: Cron job executed {"time":"2025-07-02 18:07:01"} 
[2025-07-02 18:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:07:02] local.INFO: Cron job executed {"time":"2025-07-02 18:07:02"} 
[2025-07-02 18:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:08:01] local.INFO: Cron job executed {"time":"2025-07-02 18:08:01"} 
[2025-07-02 18:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:08:02] local.INFO: Cron job executed {"time":"2025-07-02 18:08:02"} 
[2025-07-02 18:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:09:01] local.INFO: Cron job executed {"time":"2025-07-02 18:09:01"} 
[2025-07-02 18:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:09:02] local.INFO: Cron job executed {"time":"2025-07-02 18:09:02"} 
[2025-07-02 18:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:10:01] local.INFO: Cron job executed {"time":"2025-07-02 18:10:01"} 
[2025-07-02 18:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:10:02] local.INFO: Cron job executed {"time":"2025-07-02 18:10:02"} 
[2025-07-02 18:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:11:01] local.INFO: Cron job executed {"time":"2025-07-02 18:11:01"} 
[2025-07-02 18:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:11:02] local.INFO: Cron job executed {"time":"2025-07-02 18:11:02"} 
[2025-07-02 18:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:12:01] local.INFO: Cron job executed {"time":"2025-07-02 18:12:01"} 
[2025-07-02 18:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:12:02] local.INFO: Cron job executed {"time":"2025-07-02 18:12:02"} 
[2025-07-02 18:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:13:01] local.INFO: Cron job executed {"time":"2025-07-02 18:13:01"} 
[2025-07-02 18:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:13:02] local.INFO: Cron job executed {"time":"2025-07-02 18:13:02"} 
[2025-07-02 18:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:14:01] local.INFO: Cron job executed {"time":"2025-07-02 18:14:01"} 
[2025-07-02 18:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:14:03] local.INFO: Cron job executed {"time":"2025-07-02 18:14:03"} 
[2025-07-02 18:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:15:01] local.INFO: Cron job executed {"time":"2025-07-02 18:15:01"} 
[2025-07-02 18:15:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:15:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:15:03] local.INFO: Cron job executed {"time":"2025-07-02 18:15:03"} 
[2025-07-02 18:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:16:01] local.INFO: Cron job executed {"time":"2025-07-02 18:16:01"} 
[2025-07-02 18:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:16:03] local.INFO: Cron job executed {"time":"2025-07-02 18:16:03"} 
[2025-07-02 18:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:17:01] local.INFO: Cron job executed {"time":"2025-07-02 18:17:01"} 
[2025-07-02 18:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:17:02] local.INFO: Cron job executed {"time":"2025-07-02 18:17:02"} 
[2025-07-02 18:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:18:01] local.INFO: Cron job executed {"time":"2025-07-02 18:18:01"} 
[2025-07-02 18:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:18:02] local.INFO: Cron job executed {"time":"2025-07-02 18:18:02"} 
[2025-07-02 18:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:19:01] local.INFO: Cron job executed {"time":"2025-07-02 18:19:01"} 
[2025-07-02 18:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:19:02] local.INFO: Cron job executed {"time":"2025-07-02 18:19:02"} 
[2025-07-02 18:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:20:01] local.INFO: Cron job executed {"time":"2025-07-02 18:20:01"} 
[2025-07-02 18:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:20:02] local.INFO: Cron job executed {"time":"2025-07-02 18:20:02"} 
[2025-07-02 18:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:21:01] local.INFO: Cron job executed {"time":"2025-07-02 18:21:01"} 
[2025-07-02 18:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:21:02] local.INFO: Cron job executed {"time":"2025-07-02 18:21:02"} 
[2025-07-02 18:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:22:01] local.INFO: Cron job executed {"time":"2025-07-02 18:22:01"} 
[2025-07-02 18:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:22:02] local.INFO: Cron job executed {"time":"2025-07-02 18:22:02"} 
[2025-07-02 18:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:23:01] local.INFO: Cron job executed {"time":"2025-07-02 18:23:01"} 
[2025-07-02 18:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:23:02] local.INFO: Cron job executed {"time":"2025-07-02 18:23:02"} 
[2025-07-02 18:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:24:01] local.INFO: Cron job executed {"time":"2025-07-02 18:24:01"} 
[2025-07-02 18:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:24:02] local.INFO: Cron job executed {"time":"2025-07-02 18:24:02"} 
[2025-07-02 18:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:25:01] local.INFO: Cron job executed {"time":"2025-07-02 18:25:01"} 
[2025-07-02 18:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:25:02] local.INFO: Cron job executed {"time":"2025-07-02 18:25:02"} 
[2025-07-02 18:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:26:01] local.INFO: Cron job executed {"time":"2025-07-02 18:26:01"} 
[2025-07-02 18:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:26:02] local.INFO: Cron job executed {"time":"2025-07-02 18:26:02"} 
[2025-07-02 18:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:27:01] local.INFO: Cron job executed {"time":"2025-07-02 18:27:01"} 
[2025-07-02 18:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:27:02] local.INFO: Cron job executed {"time":"2025-07-02 18:27:02"} 
[2025-07-02 18:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:28:01] local.INFO: Cron job executed {"time":"2025-07-02 18:28:01"} 
[2025-07-02 18:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:28:03] local.INFO: Cron job executed {"time":"2025-07-02 18:28:03"} 
[2025-07-02 18:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:29:01] local.INFO: Cron job executed {"time":"2025-07-02 18:29:01"} 
[2025-07-02 18:29:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:29:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:29:03] local.INFO: Cron job executed {"time":"2025-07-02 18:29:03"} 
[2025-07-02 18:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:30:01] local.INFO: Cron job executed {"time":"2025-07-02 18:30:01"} 
[2025-07-02 18:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:30:03] local.INFO: Cron job executed {"time":"2025-07-02 18:30:03"} 
[2025-07-02 18:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:31:01] local.INFO: Cron job executed {"time":"2025-07-02 18:31:01"} 
[2025-07-02 18:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:31:03] local.INFO: Cron job executed {"time":"2025-07-02 18:31:03"} 
[2025-07-02 18:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:32:01] local.INFO: Cron job executed {"time":"2025-07-02 18:32:01"} 
[2025-07-02 18:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:32:02] local.INFO: Cron job executed {"time":"2025-07-02 18:32:02"} 
[2025-07-02 18:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:33:01] local.INFO: Cron job executed {"time":"2025-07-02 18:33:01"} 
[2025-07-02 18:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:33:02] local.INFO: Cron job executed {"time":"2025-07-02 18:33:02"} 
[2025-07-02 18:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:34:01] local.INFO: Cron job executed {"time":"2025-07-02 18:34:01"} 
[2025-07-02 18:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:34:02] local.INFO: Cron job executed {"time":"2025-07-02 18:34:02"} 
[2025-07-02 18:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:35:01] local.INFO: Cron job executed {"time":"2025-07-02 18:35:01"} 
[2025-07-02 18:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:35:02] local.INFO: Cron job executed {"time":"2025-07-02 18:35:02"} 
[2025-07-02 18:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:36:01] local.INFO: Cron job executed {"time":"2025-07-02 18:36:01"} 
[2025-07-02 18:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:36:02] local.INFO: Cron job executed {"time":"2025-07-02 18:36:02"} 
[2025-07-02 18:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:37:01] local.INFO: Cron job executed {"time":"2025-07-02 18:37:01"} 
[2025-07-02 18:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:37:02] local.INFO: Cron job executed {"time":"2025-07-02 18:37:02"} 
[2025-07-02 18:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:38:01] local.INFO: Cron job executed {"time":"2025-07-02 18:38:01"} 
[2025-07-02 18:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:38:02] local.INFO: Cron job executed {"time":"2025-07-02 18:38:02"} 
[2025-07-02 18:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:39:01] local.INFO: Cron job executed {"time":"2025-07-02 18:39:01"} 
[2025-07-02 18:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:39:02] local.INFO: Cron job executed {"time":"2025-07-02 18:39:02"} 
[2025-07-02 18:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:40:01] local.INFO: Cron job executed {"time":"2025-07-02 18:40:01"} 
[2025-07-02 18:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:40:02] local.INFO: Cron job executed {"time":"2025-07-02 18:40:02"} 
[2025-07-02 18:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:41:01] local.INFO: Cron job executed {"time":"2025-07-02 18:41:01"} 
[2025-07-02 18:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:41:02] local.INFO: Cron job executed {"time":"2025-07-02 18:41:02"} 
[2025-07-02 18:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:42:01] local.INFO: Cron job executed {"time":"2025-07-02 18:42:01"} 
[2025-07-02 18:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:42:03] local.INFO: Cron job executed {"time":"2025-07-02 18:42:03"} 
[2025-07-02 18:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:43:01] local.INFO: Cron job executed {"time":"2025-07-02 18:43:01"} 
[2025-07-02 18:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:43:03] local.INFO: Cron job executed {"time":"2025-07-02 18:43:03"} 
[2025-07-02 18:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:44:01] local.INFO: Cron job executed {"time":"2025-07-02 18:44:01"} 
[2025-07-02 18:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:44:02] local.INFO: Cron job executed {"time":"2025-07-02 18:44:02"} 
[2025-07-02 18:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:45:01] local.INFO: Cron job executed {"time":"2025-07-02 18:45:01"} 
[2025-07-02 18:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:45:02] local.INFO: Cron job executed {"time":"2025-07-02 18:45:02"} 
[2025-07-02 18:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:46:01] local.INFO: Cron job executed {"time":"2025-07-02 18:46:01"} 
[2025-07-02 18:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:46:02] local.INFO: Cron job executed {"time":"2025-07-02 18:46:02"} 
[2025-07-02 18:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:47:01] local.INFO: Cron job executed {"time":"2025-07-02 18:47:01"} 
[2025-07-02 18:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:47:02] local.INFO: Cron job executed {"time":"2025-07-02 18:47:02"} 
