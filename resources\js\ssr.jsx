import "../css/app.css";
import "./bootstrap";

import { createInertiaApp } from "@inertiajs/react";
import createServer from "@inertiajs/react/server";
import ReactDOMServer from "react-dom/server";
import { createRoot, hydrateRoot } from "react-dom/client";
import { store } from "./redux/store";
import { Provider } from "react-redux";
import * as Sentry from "@sentry/react";
import React from "react";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_REACT_DSN,
    sendDefaultPii: true,
    integrations: [Sentry.replayIntegration()],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
});

createServer((page) =>
    createInertiaApp({
        title: (title) => `${title} - ${appName}`,
        page,
        render: ReactDOMServer.renderToString,
        resolve: (name) => {
            const pages = import.meta.glob("./Pages/**/*.jsx", { eager: true });
            return pages[`./Pages/${name}.jsx`];
        },
        setup({ el, App, props }) {
            const root = hydrateRoot(el);

            root.render(
                <Provider store={store}>
                    <App {...props} />
                </Provider>,
            );
        },
    }),
);
