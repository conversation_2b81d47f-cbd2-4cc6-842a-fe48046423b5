;
{
  try {
    let e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {}, n = new e.Error().stack;
    n && (e._sentryDebugIds = e._sentryDebugIds || {}, e._sentryDebugIds[n] = "0a5ced45-9096-4b9e-90fa-9a45e0b7a665", e._sentryDebugIdIdentifier = "sentry-dbid-0a5ced45-9096-4b9e-90fa-9a45e0b7a665");
  } catch (e) {
  }
}
;
import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { useEffect, useState, forwardRef, useRef, useImperativeHandle, useCallback, useMemo, createElement } from "react";
import { Link, usePage, useForm, Head, router, createInertiaApp } from "@inertiajs/react";
import { useDispatch, useSelector, Provider } from "react-redux";
import { createAsyncThunk, createSlice, configureStore } from "@reduxjs/toolkit";
import axios$1 from "axios";
import { Facebook, Twitter, Instagram, Youtube, Mail, User, EyeOff, Eye, CircleCheckBig, AlertCircle, XCircle, Calendar, MapPin, ShieldCheck, Ghost, AlertTriangle, Search as Search$1, House, HousePlus, ClockAlert, Clock, ChevronLeft, ChevronRight, MapPinned, CalendarDays, ArrowRight, Star, Globe2, CreditCard, Ticket, Wallet, ShoppingBag, MessageSquare, Target, FileText, Tag, Globe, Flag, Trophy, Dribbble, Plane, Building, MailOpen, Banknote, DollarSign, Hash, Armchair, Phone, Home as Home$1, CheckCircle, RefreshCw, Download, Filter, Info, SquarePen, Trash2, Blocks, Tickets, Frown } from "lucide-react";
import toast, { Toaster } from "react-hot-toast";
import Select from "react-select";
import useInfiniteScroll from "react-infinite-scroll-hook";
import { useDebounce } from "@uidotdev/usehooks";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc.js";
import timezone from "dayjs/plugin/timezone.js";
import advancedFormat from "dayjs/plugin/advancedFormat.js";
import { Range, getTrackBackground } from "react-range";
import debounce from "lodash/debounce.js";
import { Transition, Dialog, TransitionChild, DialogPanel } from "@headlessui/react";
import { useStripe, useElements, CardElement, Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import createServer from "@inertiajs/react/server";
import ReactDOMServer from "react-dom/server";
import { hydrateRoot } from "react-dom/client";
import * as Sentry from "@sentry/react";
{
  let _global = typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : {};
  _global.SENTRY_RELEASE = { id: "7ea606dd4313d601c9e86d7a29f2078527d40bad" };
}
const CACHE_DURATION_MS = 1e3 * 60 * 60;
function getStorageKey(locale) {
  return `tg_translations_${locale}`;
}
function loadFromStorage(locale) {
  try {
    const data = localStorage.getItem(getStorageKey(locale));
    if (!data) return null;
    const { translations, timestamp, version } = JSON.parse(data);
    if (version !== "2025-07-02T18:54:39.960Z") {
      localStorage.removeItem(getStorageKey(locale));
      return null;
    }
    if (Date.now() - timestamp > CACHE_DURATION_MS) {
      localStorage.removeItem(getStorageKey(locale));
      return null;
    }
    return translations;
  } catch {
    return null;
  }
}
function saveToStorage(locale, translations) {
  localStorage.setItem(
    getStorageKey(locale),
    JSON.stringify({
      translations,
      timestamp: Date.now(),
      version: "2025-07-02T18:54:39.960Z"
    })
  );
}
const fetchTranslations = createAsyncThunk(
  "translations/fetch",
  async ({ locale, url }, { getState }) => {
    const existing = getState().translations.data[locale];
    if (existing) return { locale, translations: existing };
    const cached = loadFromStorage(locale);
    if (cached) return { locale, translations: cached };
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return { locale, translations: response.data.translations };
    } else {
      throw new Error("Failed to fetch translations");
    }
  }
);
const initialState$e = {
  data: {},
  loading: {}
};
const translationSlice = createSlice({
  name: "translations",
  initialState: initialState$e,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchTranslations.pending, (state, action) => {
      state.loading[action.meta.arg.locale] = true;
    }).addCase(fetchTranslations.fulfilled, (state, action) => {
      const { locale, translations } = action.payload;
      state.data[locale] = translations;
      state.loading[locale] = false;
      saveToStorage(locale, translations);
    }).addCase(fetchTranslations.rejected, (state, action) => {
      state.loading[action.meta.arg.locale] = false;
    });
  }
});
const translationReducer = translationSlice.reducer;
const loadingLocales = /* @__PURE__ */ new Set();
function useTranslations() {
  const locale = document.documentElement.lang;
  const dispatch = useDispatch();
  const translations = useSelector(
    (state) => state.translations.data[locale]
  );
  useEffect(() => {
    if (!translations || Object.keys(translations).length === 0) {
      if (!loadingLocales.has(locale)) {
        loadingLocales.add(locale);
        dispatch(
          fetchTranslations({
            locale,
            url: route("api.translations.index")
          })
        ).finally(() => {
          loadingLocales.delete(locale);
        });
      }
    }
  }, [locale]);
  const translate = (key, fallback = "") => key.split(".").reduce((acc, part) => acc == null ? void 0 : acc[part], translations) ?? fallback;
  return { translate };
}
function Footer() {
  return /* @__PURE__ */ jsx("footer", { className: "bg-neutral text-neutral-content", children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4", children: [
    /* @__PURE__ */ jsxs("div", { className: "footer py-10", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { className: "footer-title", children: "Company" }),
        /* @__PURE__ */ jsx(Link, { href: "/about", className: "link link-hover", children: "About us" }),
        /* @__PURE__ */ jsx(Link, { href: "/contact", className: "link link-hover", children: "Contact" }),
        /* @__PURE__ */ jsx(Link, { href: "/careers", className: "link link-hover", children: "Careers" }),
        /* @__PURE__ */ jsx(Link, { href: "/press", className: "link link-hover", children: "Press kit" })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { className: "footer-title", children: "Legal" }),
        /* @__PURE__ */ jsx(Link, { href: "/terms", className: "link link-hover", children: "Terms of use" }),
        /* @__PURE__ */ jsx(Link, { href: "/privacy", className: "link link-hover", children: "Privacy policy" }),
        /* @__PURE__ */ jsx(Link, { href: "/cookie", className: "link link-hover", children: "Cookie policy" })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { className: "footer-title", children: "Social" }),
        /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
          /* @__PURE__ */ jsx("a", { href: "#", className: "btn btn-ghost btn-square", children: /* @__PURE__ */ jsx(Facebook, { className: "w-5 h-5" }) }),
          /* @__PURE__ */ jsx("a", { href: "#", className: "btn btn-ghost btn-square", children: /* @__PURE__ */ jsx(Twitter, { className: "w-5 h-5" }) }),
          /* @__PURE__ */ jsx("a", { href: "#", className: "btn btn-ghost btn-square", children: /* @__PURE__ */ jsx(Instagram, { className: "w-5 h-5" }) }),
          /* @__PURE__ */ jsx("a", { href: "#", className: "btn btn-ghost btn-square", children: /* @__PURE__ */ jsx(Youtube, { className: "w-5 h-5" }) })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { className: "footer-title", children: "Newsletter" }),
        /* @__PURE__ */ jsxs("div", { className: "form-control w-full md:w-80", children: [
          /* @__PURE__ */ jsx("label", { className: "label", children: /* @__PURE__ */ jsx("span", { className: "label-text text-neutral-content", children: "Stay updated with our latest events!" }) }),
          /* @__PURE__ */ jsxs("div", { className: "join", children: [
            /* @__PURE__ */ jsx(
              "input",
              {
                type: "text",
                placeholder: "Enter your email",
                className: "input input-bordered join-item w-full"
              }
            ),
            /* @__PURE__ */ jsx("button", { className: "btn btn-primary join-item", children: /* @__PURE__ */ jsx(Mail, { className: "w-5 h-5" }) })
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "footer footer-center p-4 border-t border-base-300", children: /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx("p", { children: "Copyright © 2025 TicketGol - All rights reserved" }) }) })
  ] }) });
}
function NavLink({
  isExact = false,
  className = "",
  children,
  ...props
}) {
  const currentPath = window.location.pathname;
  const isActive = isExact ? currentPath === "/" : props.href.includes(currentPath) && currentPath !== "/";
  return /* @__PURE__ */ jsx(
    Link,
    {
      ...props,
      className: "text-base focus:text-neutral-content hover:underline hover:underline-offset-8 " + (isActive ? " underline underline-offset-8 font-extrabold " : "") + className,
      children
    }
  );
}
function useLanguage(defaultLocale = "en") {
  const [locale, setLocale] = useState(defaultLocale);
  const getCookie = (name) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(";").shift();
    return defaultLocale;
  };
  const handleLanguageChange = (newLocale) => {
    setLocale(newLocale);
    document.cookie = `selected_locale=${newLocale}; path=/; max-age=31536000`;
    window.location.reload();
  };
  useEffect(() => {
    const cookieLocale = getCookie("selected_locale");
    setLocale(cookieLocale);
  }, []);
  return {
    locale,
    handleLanguageChange
  };
}
function LanguageDropdown({ translate }) {
  var _a;
  const { locale, handleLanguageChange } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const languages = Object.entries(translate("enums.languages"));
  const selectedLabel = ((_a = languages.find(([key]) => key === locale)) == null ? void 0 : _a[1]) || "Select";
  return /* @__PURE__ */ jsxs("div", { className: "dropdown dropdown-end", children: [
    /* @__PURE__ */ jsx(
      "div",
      {
        tabIndex: 0,
        role: "button",
        className: "btn btn-sm btn-outline border-amber-400 text-amber-400 hover:bg-amber-500 hover:text-white w-full max-w-xs",
        onClick: () => setIsOpen(!isOpen),
        children: selectedLabel
      }
    ),
    isOpen && /* @__PURE__ */ jsx(
      "ul",
      {
        tabIndex: 0,
        className: "dropdown-content menu p-1 shadow bg-base-100 rounded-box w-40 z-20",
        children: languages.map(([key, value]) => /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
          "a",
          {
            className: "text-gray-700",
            onClick: () => {
              handleLanguageChange(key);
              setIsOpen(false);
            },
            children: value
          }
        ) }, key))
      }
    )
  ] });
}
function Navbar() {
  const { auth } = usePage().props;
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs("div", { className: "navbar bg-gray-800 text-neutral-content shadow-md", children: [
    /* @__PURE__ */ jsxs("div", { className: "navbar-start", children: [
      /* @__PURE__ */ jsxs("div", { className: "dropdown", children: [
        /* @__PURE__ */ jsx(
          "div",
          {
            tabIndex: 0,
            role: "button",
            className: "btn btn-ghost lg:hidden",
            children: /* @__PURE__ */ jsx(
              "svg",
              {
                xmlns: "http://www.w3.org/2000/svg",
                className: "h-6 w-6",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor",
                children: /* @__PURE__ */ jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    d: "M4 6h16M4 12h8m-8 6h16"
                  }
                )
              }
            )
          }
        ),
        /* @__PURE__ */ jsxs(
          "ul",
          {
            tabIndex: 0,
            className: "menu menu-sm dropdown-content mt-3 z-[20] p-2 shadow bg-base-100 rounded-box w-52",
            children: [
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                NavLink,
                {
                  href: "/",
                  isExact: true,
                  className: "text-gray-700",
                  children: translate("common.menu.home")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                NavLink,
                {
                  href: route("events"),
                  className: "text-gray-700",
                  children: translate("common.menu.events")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                NavLink,
                {
                  href: route("stadiums"),
                  className: "text-gray-700",
                  children: translate("common.menu.stadiums")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                NavLink,
                {
                  href: route("clubs"),
                  className: "text-gray-700",
                  children: translate("common.menu.clubs")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                NavLink,
                {
                  href: route("leagues"),
                  className: "text-gray-700",
                  children: translate("common.menu.leagues")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: "/about", className: "text-gray-700", children: translate("common.menu.about") }) })
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx(Link, { href: route("home"), children: /* @__PURE__ */ jsx(
        "img",
        {
          src: "/img/ticketgol-logo.png",
          className: "sm:ml-2 h-5 sm:h-6 lg:h-8"
        }
      ) })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "navbar-center hidden lg:flex", children: /* @__PURE__ */ jsxs("ul", { className: "menu menu-horizontal px-1", children: [
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: route("home"), isExact: true, children: translate("common.menu.home") }) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: route("events"), children: translate("common.menu.events") }) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: route("stadiums"), children: translate("common.menu.stadiums") }) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: route("clubs"), children: translate("common.menu.clubs") }) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: route("leagues"), children: translate("common.menu.leagues") }) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(NavLink, { href: "/about", children: translate("common.menu.about") }) })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "navbar-end", children: [
      /* @__PURE__ */ jsx("div", { className: "lg:mr-4 mr-2", children: /* @__PURE__ */ jsx(LanguageDropdown, { translate }) }),
      (auth == null ? void 0 : auth.user) ? /* @__PURE__ */ jsxs("div", { className: "dropdown dropdown-end", children: [
        /* @__PURE__ */ jsxs(
          "div",
          {
            tabIndex: "0",
            role: "button",
            className: "btn btn-sm bg-amber-400 border-amber-400 text-white hover:bg-amber-500 hover:border-amber-500 m-1",
            children: [
              /* @__PURE__ */ jsx(User, { className: "w-5 h-5 inline-block" }),
              /* @__PURE__ */ jsx("span", { className: "hidden sm:flex", children: auth.user.name })
            ]
          }
        ),
        /* @__PURE__ */ jsxs(
          "ul",
          {
            tabIndex: "0",
            className: "dropdown-content menu bg-base-100 rounded-box z-20 w-52 p-2 shadow-sm",
            children: [
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                Link,
                {
                  href: route("my-account.index"),
                  className: "text-gray-700 px-3",
                  children: translate("common.menu.my_account")
                }
              ) }),
              /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
                Link,
                {
                  href: route("logout"),
                  method: "post",
                  as: "button",
                  className: "text-gray-700 px-3",
                  children: translate("common.menu.logout")
                }
              ) })
            ]
          }
        )
      ] }) : /* @__PURE__ */ jsxs(
        Link,
        {
          href: route("login"),
          className: "btn btn-sm bg-amber-400 border-amber-400 text-white hover:bg-amber-500 hover:border-amber-500",
          children: [
            /* @__PURE__ */ jsx(User, { className: "w-4 h-4 inline-block" }),
            /* @__PURE__ */ jsx("span", { children: translate("common.login_btn") })
          ]
        }
      )
    ] })
  ] });
}
function FlashToast() {
  const { flash } = usePage().props;
  useEffect(() => {
    if (flash.success) {
      toast.success(flash.success);
    }
    if (flash.error) {
      toast.error(flash.error);
    }
  }, [flash.success, flash.error]);
  return /* @__PURE__ */ jsx(
    Toaster,
    {
      position: "top-right",
      duration: 2e3,
      containerStyle: {
        top: 100,
        left: 20,
        bottom: 20,
        right: 50
      }
    }
  );
}
function AppLayout$1({ children }) {
  return /* @__PURE__ */ jsxs("div", { className: "min-h-screen flex flex-col", children: [
    /* @__PURE__ */ jsx(FlashToast, {}),
    /* @__PURE__ */ jsx(Navbar, {}),
    /* @__PURE__ */ jsx("main", { className: "flex-1 bg-base-200", children }),
    /* @__PURE__ */ jsx(Footer, {})
  ] });
}
function InputError({ message, className = "", ...props }) {
  return message ? /* @__PURE__ */ jsx("p", { ...props, className: "text-sm text-red-600 " + className, children: message }) : null;
}
function InputLabel({
  value,
  className = "",
  children,
  ...props
}) {
  return /* @__PURE__ */ jsx("label", { ...props, className: `label text-sm font-medium ` + className, children: value ? value : children });
}
const PasswordInput = forwardRef(function PasswordInput2({ className = "", isFocused = false, ...props }, ref) {
  const [showPassword, setShowPassword] = useState(false);
  const localRef = useRef(null);
  useImperativeHandle(ref, () => ({
    focus: () => {
      var _a;
      return (_a = localRef.current) == null ? void 0 : _a.focus();
    }
  }));
  useEffect(() => {
    var _a;
    if (isFocused) {
      (_a = localRef.current) == null ? void 0 : _a.focus();
    }
  }, [isFocused]);
  const { error, ...inputProps } = props;
  return /* @__PURE__ */ jsxs("div", { className: "form-control mt-2", children: [
    props.label && /* @__PURE__ */ jsx(
      InputLabel,
      {
        htmlFor: props.id,
        value: props.label,
        className: `${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`
      }
    ),
    /* @__PURE__ */ jsxs("label", { className: "input input-bordered flex items-center gap-2", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          ...inputProps,
          type: showPassword ? "text" : "password",
          className: `grow ${className}`,
          ref: localRef
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          onClick: () => setShowPassword(!showPassword),
          children: showPassword ? /* @__PURE__ */ jsx(EyeOff, { className: "w-5 h-5" }) : /* @__PURE__ */ jsx(Eye, { className: "w-5 h-5" })
        }
      )
    ] }),
    error && /* @__PURE__ */ jsx(InputError, { message: error, className: "mt-2" })
  ] });
});
function PrimaryButton({
  className = "",
  disabled,
  children,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    "button",
    {
      ...props,
      className: `btn btn-primary ${disabled && "opacity-75"} ` + className,
      disabled,
      children
    }
  );
}
function ConfirmPassword() {
  const { translate } = useTranslations();
  const [showPassword, setShowPassword] = useState(false);
  const { data, setData, post, processing, errors, reset } = useForm({
    password: ""
  });
  const submit = (e) => {
    e.preventDefault();
    post(route("password.confirm"), {
      onFinish: () => reset("password")
    });
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("password.confirm_page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-md bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("password.confirm_page_title") }),
      /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm text-gray-600", children: translate("password.confirm_page_description") }),
      /* @__PURE__ */ jsxs("form", { onSubmit: submit, children: [
        /* @__PURE__ */ jsx(
          PasswordInput,
          {
            id: "password",
            name: "password",
            value: data.password,
            placeholder: translate("common.password"),
            autoComplete: "current-password",
            onChange: (e) => setData("password", e.target.value)
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "form-control mt-6", children: /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("password.confirm_btn") }) })
      ] })
    ] }) }) })
  ] });
}
ConfirmPassword.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ConfirmPassword
}, Symbol.toStringTag, { value: "Module" }));
const TextInput = forwardRef(function TextInput2({ type = "text", className = "", isFocused = false, ...props }, ref) {
  const localRef = useRef(null);
  useImperativeHandle(ref, () => ({
    focus: () => {
      var _a;
      return (_a = localRef.current) == null ? void 0 : _a.focus();
    }
  }));
  useEffect(() => {
    var _a;
    if (isFocused) {
      (_a = localRef.current) == null ? void 0 : _a.focus();
    }
  }, [isFocused]);
  const { error, ...inputProps } = props;
  const baseClass = (type === "textarea" ? "textarea textarea-bordered " : "input input-bordered ") + className + (props.readOnly ? " bg-gray-200 cursor-not-allowed" : "") + (error ? " input-error" : "");
  return /* @__PURE__ */ jsxs("div", { className: "form-control mt-2", children: [
    props.label && /* @__PURE__ */ jsx(
      InputLabel,
      {
        htmlFor: props.id,
        value: props.label,
        className: `${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`
      }
    ),
    type === "textarea" ? /* @__PURE__ */ jsx(
      "textarea",
      {
        ...inputProps,
        className: baseClass,
        placeholder: props.datarequired ? `${props.placeholder} *` : props.placeholder,
        ref: localRef
      }
    ) : /* @__PURE__ */ jsx(
      "input",
      {
        ...inputProps,
        type,
        className: baseClass,
        placeholder: props.datarequired ? `${props.placeholder} *` : props.placeholder,
        ref: localRef
      }
    ),
    error && /* @__PURE__ */ jsx(InputError, { message: error, className: "mt-2" })
  ] });
});
function ForgotPassword({ status }) {
  const { translate } = useTranslations();
  const { data, setData, post, processing, errors } = useForm({
    email: ""
  });
  const submit = (e) => {
    e.preventDefault();
    post(route("password.email"));
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("password.forgot_page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-md bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("password.forgot_page_title") }),
      /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm text-gray-600", children: translate("password.forgot_page_description") }),
      status && /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm font-medium text-green-600", children: status }),
      /* @__PURE__ */ jsxs("form", { onSubmit: submit, children: [
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "email",
            type: "email",
            name: "email",
            value: data.email,
            placeholder: translate(
              "common.placeholder.email"
            ),
            isFocused: true,
            onChange: (e) => setData("email", e.target.value),
            error: errors.email
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "mt-4 form-control", children: /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate(
          "password.reset_password_link_btn"
        ) }) })
      ] })
    ] }) }) })
  ] });
}
ForgotPassword.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ForgotPassword
}, Symbol.toStringTag, { value: "Module" }));
function Checkbox({ className = "", ...props }) {
  return /* @__PURE__ */ jsx(
    "input",
    {
      ...props,
      type: "checkbox",
      className: "rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 " + className
    }
  );
}
function Login({ status }) {
  const { translate } = useTranslations();
  const [showPassword, setShowPassword] = useState(false);
  const getRedirectParam = () => {
    const params = new URLSearchParams(window.location.search);
    return decodeURIComponent(params.get("redirect") ?? "/dashboard");
  };
  const { data, setData, post, processing, errors, reset } = useForm({
    email: "",
    password: "",
    remember: false,
    redirect: getRedirectParam()
  });
  const submit = (e) => {
    e.preventDefault();
    post(route("login"), {
      onFinish: () => reset("password")
    });
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("login.page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-md bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("login.page_title") }),
      status && /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm font-medium text-green-600", children: status }),
      /* @__PURE__ */ jsxs("form", { onSubmit: submit, children: [
        /* @__PURE__ */ jsxs("div", { className: "grid gap-4", children: [
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "email",
              type: "email",
              name: "email",
              value: data.email,
              autoComplete: "username",
              placeholder: translate(
                "common.placeholder.email"
              ),
              isFocused: true,
              onChange: (e) => setData("email", e.target.value),
              error: errors.email
            }
          ),
          /* @__PURE__ */ jsx(
            PasswordInput,
            {
              id: "password",
              name: "password",
              value: data.password,
              placeholder: translate(
                "common.placeholder.password"
              ),
              autoComplete: "current-password",
              onChange: (e) => setData("password", e.target.value),
              error: errors.password
            }
          )
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "mt-4 flex justify-between items-center", children: [
          /* @__PURE__ */ jsxs("label", { className: "flex items-center", children: [
            /* @__PURE__ */ jsx(
              Checkbox,
              {
                name: "remember",
                checked: data.remember,
                onChange: (e) => setData(
                  "remember",
                  e.target.checked
                )
              }
            ),
            /* @__PURE__ */ jsx("span", { className: "ms-2 text-sm text-gray-600", children: translate("login.remember_me") })
          ] }),
          /* @__PURE__ */ jsx(
            Link,
            {
              href: route("password.request"),
              className: "label-text-alt link link-hover",
              children: translate("login.forgot_pass_text")
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "form-control mt-6", children: /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("login.page_title") }) }),
        /* @__PURE__ */ jsx("div", { className: "divider", children: translate("login.or_text") }),
        /* @__PURE__ */ jsxs("p", { className: "text-center", children: [
          translate("login.dont_have_account"),
          /* @__PURE__ */ jsx(
            Link,
            {
              href: route("register"),
              className: "link link-primary ms-2",
              children: translate("common.register_btn")
            }
          )
        ] })
      ] })
    ] }) }) })
  ] });
}
Login.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Login
}, Symbol.toStringTag, { value: "Module" }));
const SelectInput = forwardRef(function SelectInput2({ className = "", options = [], wrapperClass = "", ...props }, ref) {
  const localRef = useRef(null);
  const { error, ...selectProps } = props;
  const customStyles = {
    control: (base, state) => ({
      ...base,
      backgroundColor: "white",
      borderColor: error ? "red" : "#1f293733",
      borderWidth: "1px",
      borderRadius: "8px",
      padding: "5px",
      outline: state.isFocused ? "2px solid #1f293733" : "none",
      outlineOffset: state.isFocused ? "2px" : "",
      boxShadow: "none",
      "&:hover": {
        borderColor: error ? "red" : "#1f293733"
      }
    }),
    menuPortal: (base) => ({ ...base, zIndex: 999 })
  };
  return /* @__PURE__ */ jsxs("div", { className: `form-control mt-2 ${wrapperClass = ""}`, children: [
    props.label && /* @__PURE__ */ jsx(
      InputLabel,
      {
        htmlFor: props.id,
        value: props.label,
        className: `${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`
      }
    ),
    /* @__PURE__ */ jsx(
      Select,
      {
        ...selectProps,
        options,
        className: `w-full ${className}`,
        styles: customStyles,
        placeholder: props.datarequired ? `${props.placeholder} *` : props.placeholder,
        classNamePrefix: "daisyui-select",
        isSearchable: true,
        ref: localRef
      }
    ),
    error && /* @__PURE__ */ jsx(InputError, { message: error, className: "mt-2" })
  ] });
});
function Register({ countries, genders }) {
  const { translate } = useTranslations();
  const { data, setData, post, processing, errors, reset } = useForm({
    name: "",
    surname: "",
    user_name: "",
    email: "",
    password: "",
    password_confirmation: "",
    phone: "",
    gender: "",
    address: "",
    city: "",
    country_id: "",
    zip: ""
  });
  const genderOptions = Object.entries(genders).map(([key, value]) => ({
    value: key,
    label: value
  }));
  const countryOptions = countries.map((country) => ({
    value: country.id,
    label: country.translation.name
  }));
  const submit = (e) => {
    e.preventDefault();
    post(route("register"), {
      onFinish: () => reset("password", "password_confirmation")
    });
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("register.page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-2xl bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("register.page_title") }),
      /* @__PURE__ */ jsxs("form", { onSubmit: submit, children: [
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "name",
              name: "name",
              value: data.name,
              placeholder: translate(
                "common.placeholder.name"
              ),
              datarequired: "true",
              label: translate("common.labels.name"),
              isFocused: true,
              onChange: (e) => setData("name", e.target.value),
              error: errors.name
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "surname",
              name: "surname",
              value: data.surname,
              placeholder: translate(
                "common.placeholder.surname"
              ),
              datarequired: "true",
              label: translate("common.labels.surname"),
              onChange: (e) => setData("surname", e.target.value),
              error: errors.surname
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "user_name",
              name: "user_name",
              value: data.user_name,
              placeholder: translate(
                "common.placeholder.user_name"
              ),
              datarequired: "true",
              label: translate("common.labels.user_name"),
              onChange: (e) => setData("user_name", e.target.value),
              error: errors.user_name
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "email",
              type: "email",
              name: "email",
              value: data.email,
              autoComplete: "username",
              placeholder: translate(
                "common.placeholder.email"
              ),
              datarequired: "true",
              label: translate("common.labels.email"),
              onChange: (e) => setData("email", e.target.value),
              error: errors.email
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "phone",
              name: "phone",
              type: "tel",
              value: data.phone,
              placeholder: translate(
                "common.placeholder.phone"
              ),
              datarequired: "true",
              label: translate("common.labels.phone"),
              maxLength: "15",
              onChange: (e) => setData("phone", e.target.value),
              error: errors.phone
            }
          ),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              id: "gender",
              name: "gender",
              placeholder: translate(
                "common.placeholder.gender"
              ),
              datarequired: "true",
              label: translate("common.labels.gender"),
              options: genderOptions,
              value: genderOptions.find(
                (option) => option.value === data.gender
              ),
              onChange: (selectedOption) => setData("gender", selectedOption.value),
              error: errors.gender
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "address",
              name: "address",
              value: data.address,
              placeholder: translate(
                "common.placeholder.address"
              ),
              datarequired: "true",
              label: translate("common.labels.address"),
              onChange: (e) => setData("address", e.target.value),
              error: errors.address
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "city",
              name: "city",
              value: data.city,
              placeholder: translate(
                "common.placeholder.city"
              ),
              datarequired: "true",
              label: translate("common.labels.city"),
              onChange: (e) => setData("city", e.target.value),
              error: errors.city
            }
          ),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              id: "country_id",
              name: "country_id",
              placeholder: translate(
                "common.placeholder.country"
              ),
              datarequired: "true",
              label: translate("common.labels.country"),
              options: countryOptions,
              value: countryOptions.find(
                (option) => option.value === data.country_id
              ),
              onChange: (selectedOption) => setData(
                "country_id",
                selectedOption.value
              ),
              error: errors.country_id
            }
          ),
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "zip",
              name: "zip",
              value: data.zip,
              placeholder: translate(
                "common.placeholder.zip"
              ),
              datarequired: "true",
              label: translate("common.labels.zip"),
              onChange: (e) => setData("zip", e.target.value),
              error: errors.zip
            }
          ),
          /* @__PURE__ */ jsx(
            PasswordInput,
            {
              id: "password",
              name: "password",
              value: data.password,
              placeholder: translate(
                "common.placeholder.password"
              ),
              datarequired: "true",
              label: translate("common.labels.password"),
              autoComplete: "new-password",
              onChange: (e) => setData("password", e.target.value),
              error: errors.password
            }
          ),
          /* @__PURE__ */ jsx(
            PasswordInput,
            {
              id: "password_confirmation",
              name: "password_confirmation",
              value: data.password_confirmation,
              placeholder: translate(
                "common.placeholder.password_confirmation"
              ),
              datarequired: "true",
              label: translate(
                "common.labels.password_confirmation"
              ),
              autoComplete: "new-password",
              onChange: (e) => setData(
                "password_confirmation",
                e.target.value
              ),
              error: errors.password_confirmation
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "form-control mt-6 items-end", children: /* @__PURE__ */ jsx(
          Link,
          {
            href: route("login"),
            className: "rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",
            children: translate("common.already_registered")
          }
        ) }),
        /* @__PURE__ */ jsx("div", { className: "form-control mt-6", children: /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("common.register_btn") }) })
      ] })
    ] }) }) })
  ] });
}
Register.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Register
}, Symbol.toStringTag, { value: "Module" }));
function ResetPassword({ token, email }) {
  const { translate } = useTranslations();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { data, setData, post, processing, errors, reset } = useForm({
    token,
    email,
    password: "",
    password_confirmation: ""
  });
  const submit = (e) => {
    e.preventDefault();
    post(route("password.store"), {
      onFinish: () => reset("password", "password_confirmation")
    });
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("password.reset_page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-md bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("password.reset_page_title") }),
      /* @__PURE__ */ jsxs("form", { onSubmit: submit, children: [
        /* @__PURE__ */ jsxs("div", { className: "grid gap-4", children: [
          /* @__PURE__ */ jsx(
            TextInput,
            {
              id: "email",
              type: "email",
              name: "email",
              value: data.email,
              autoComplete: "username",
              onChange: (e) => setData("email", e.target.value),
              readOnly: true,
              error: errors.email
            }
          ),
          /* @__PURE__ */ jsx(
            PasswordInput,
            {
              id: "password",
              name: "password",
              value: data.password,
              placeholder: translate(
                "common.placeholder.password"
              ),
              autoComplete: "new-password",
              isFocused: true,
              onChange: (e) => setData("password", e.target.value),
              error: errors.password
            }
          ),
          /* @__PURE__ */ jsx(
            PasswordInput,
            {
              id: "password_confirmation",
              name: "password_confirmation",
              placeholder: translate(
                "common.placeholder.password_confirmation"
              ),
              value: data.password_confirmation,
              autoComplete: "new-password",
              onChange: (e) => setData(
                "password_confirmation",
                e.target.value
              ),
              error: errors.password_confirmation
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "mt-4 form-control", children: /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("password.reset_password_btn") }) })
      ] })
    ] }) }) })
  ] });
}
ResetPassword.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ResetPassword
}, Symbol.toStringTag, { value: "Module" }));
function VerifyEmail({ status }) {
  const { translate } = useTranslations();
  const { post, processing } = useForm({});
  const submit = (e) => {
    e.preventDefault();
    post(route("verification.send"));
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("verification.page_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex py-10 items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "card w-full max-w-md bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h5", { className: "card-title justify-center", children: translate("verification.page_title") }),
      /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm text-gray-600", children: translate("verification.page_description") }),
      status === "verification-link-sent" && /* @__PURE__ */ jsx("div", { className: "mb-4 text-sm font-medium text-green-600", children: translate("verification.link_sent_message") }),
      /* @__PURE__ */ jsx("form", { onSubmit: submit, children: /* @__PURE__ */ jsxs("div", { className: "mt-4 flex items-center justify-between", children: [
        /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("verification.resend_btn") }),
        /* @__PURE__ */ jsx(
          Link,
          {
            href: route("logout"),
            method: "post",
            as: "button",
            className: "rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",
            children: translate("common.menu.logout")
          }
        )
      ] }) })
    ] }) }) })
  ] });
}
VerifyEmail.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: VerifyEmail
}, Symbol.toStringTag, { value: "Module" }));
function CMSPage({ cmsData }) {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: cmsData.translation.title }),
      /* @__PURE__ */ jsx("meta", { name: "title", content: cmsData.translation.meta_title }),
      /* @__PURE__ */ jsx(
        "meta",
        {
          name: "description",
          content: cmsData.translation.meta_description
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "div",
      {
        className: "prose",
        dangerouslySetInnerHTML: {
          __html: cmsData.translation.content
        }
      }
    )
  ] });
}
CMSPage.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: CMSPage
}, Symbol.toStringTag, { value: "Module" }));
function CheckoutCancel({ sessionId }) {
  const { translate } = useTranslations();
  useEffect(() => {
    const handleCancel = async () => {
      try {
        const response = await axios.post(
          route("api.checkout.cancel"),
          { session_id: sessionId }
        );
      } catch (error) {
      }
    };
    handleCancel();
  }, []);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("checkout.cancel_head_title") }),
    /* @__PURE__ */ jsx("div", { className: "py-12", children: /* @__PURE__ */ jsx("div", { className: "mx-auto max-w-7xl sm:px-6 lg:px-8", children: /* @__PURE__ */ jsx("div", { className: "overflow-hidden bg-white shadow-sm sm:rounded-lg", children: /* @__PURE__ */ jsx("div", { className: "p-6 text-gray-900", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx(
        "svg",
        {
          className: "mx-auto h-12 w-12 text-red-500",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          xmlns: "http://www.w3.org/2000/svg",
          children: /* @__PURE__ */ jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M6 18L18 6M6 6l12 12"
            }
          )
        }
      ),
      /* @__PURE__ */ jsx("h2", { className: "mt-4 text-2xl font-bold", children: translate("checkout.payment_cancelled") }),
      /* @__PURE__ */ jsx("p", { className: "mt-2 text-gray-600", children: translate(
        "checkout.payment_cancelled_desc"
      ) }),
      /* @__PURE__ */ jsx("div", { className: "mt-6", children: /* @__PURE__ */ jsx(
        Link,
        {
          href: route("dashboard"),
          className: "inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150",
          children: translate(
            "checkout.return_to_dashboard"
          )
        }
      ) })
    ] }) }) }) }) })
  ] });
}
CheckoutCancel.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: CheckoutCancel
}, Symbol.toStringTag, { value: "Module" }));
function CheckoutSuccess({ orderId }) {
  const { translate } = useTranslations();
  const [isPaymentVerified, setIsPaymentVerified] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [verificationFailed, setVerificationFailed] = useState(false);
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 3e3;
  useEffect(() => {
    const verifyPayment = async () => {
      if (retryCount >= MAX_RETRIES) {
        setVerificationFailed(true);
        return;
      }
      try {
        const response = await axios.post(
          route("api.orders.check-status"),
          { order_id: orderId }
        );
        console.log(
          `Verification attempt ${retryCount + 1}:`,
          response
        );
        if (response.data.is_completed) {
          setIsPaymentVerified(true);
        } else {
          setTimeout(() => {
            setRetryCount((prevCount) => prevCount + 1);
          }, RETRY_DELAY);
        }
      } catch (error) {
        console.log(
          `Verification attempt ${retryCount + 1} failed:`,
          error
        );
        setTimeout(() => {
          setRetryCount((prevCount) => prevCount + 1);
        }, RETRY_DELAY);
      }
    };
    verifyPayment();
  }, [retryCount]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("checkout.success_head_title") }),
    /* @__PURE__ */ jsx("div", { className: "py-12", children: /* @__PURE__ */ jsx("div", { className: "mx-auto max-w-7xl sm:px-6 lg:px-8", children: /* @__PURE__ */ jsx("div", { className: "overflow-hidden bg-white shadow-sm sm:rounded-lg", children: /* @__PURE__ */ jsx("div", { className: "p-6 py-24 text-gray-900", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      isPaymentVerified ? /* @__PURE__ */ jsx(
        CircleCheckBig,
        {
          className: "mx-auto my-6 text-green-500",
          size: 48
        }
      ) : verificationFailed ? /* @__PURE__ */ jsx(
        AlertCircle,
        {
          className: "mx-auto my-6 text-amber-500",
          size: 48
        }
      ) : /* @__PURE__ */ jsxs("div", { className: "mx-auto my-6 w-12 h-12 rounded-full bg-gray-200 animate-pulse flex items-center justify-center relative", children: [
        /* @__PURE__ */ jsx("div", { className: "w-8 h-8 rounded-full bg-gray-300 absolute animate-ping" }),
        /* @__PURE__ */ jsx("div", { className: "w-6 h-6 rounded-full border-2 border-gray-400 border-t-transparent animate-spin" })
      ] }),
      /* @__PURE__ */ jsx("h2", { className: "mt-4 text-2xl font-bold", children: isPaymentVerified ? translate("checkout.payment_verified") : verificationFailed ? translate(
        "checkout.verification_taken_longer_text"
      ) : `${translate("checkout.wait_text")} (${translate("checkout.attempt_text")} ${retryCount + 1}/${MAX_RETRIES})` }),
      isPaymentVerified && /* @__PURE__ */ jsx("p", { className: "mt-2 text-gray-600", children: translate(
        "checkout.tickets_reserved_text"
      ) }),
      verificationFailed && /* @__PURE__ */ jsxs("div", { className: "mt-2 text-gray-600", children: [
        /* @__PURE__ */ jsx("p", { children: translate(
          "checkout.processing_payment_text"
        ) }),
        /* @__PURE__ */ jsx("p", { className: "mt-2", children: translate(
          "checkout.processing_payment_desc"
        ) }),
        /* @__PURE__ */ jsx("p", { className: "mt-2 text-sm text-gray-500", children: translate(
          "checkout.contact_support"
        ) })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "mt-6", children: (isPaymentVerified || verificationFailed) && /* @__PURE__ */ jsx(
        Link,
        {
          href: route("dashboard"),
          className: "inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150",
          children: translate(
            "checkout.return_to_dashboard"
          )
        }
      ) })
    ] }) }) }) }) })
  ] });
}
CheckoutSuccess.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_8 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: CheckoutSuccess
}, Symbol.toStringTag, { value: "Module" }));
function ClubCard({ club }) {
  return /* @__PURE__ */ jsxs("div", { className: "card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow", children: [
    /* @__PURE__ */ jsx("figure", { className: "relative h-48 bg-gray-200", children: club.image !== "" ? /* @__PURE__ */ jsx(
      "img",
      {
        src: club.image,
        alt: club.image_alt || club.name,
        className: "w-full h-full object-cover"
      }
    ) : /* @__PURE__ */ jsx("img", { src: "/img/ticketgol-logo.png", alt: club.name }) }),
    /* @__PURE__ */ jsx("div", { className: "card-body", children: /* @__PURE__ */ jsx(
      "div",
      {
        className: "tooltip tooltip-neutral w-fit text-left",
        "data-tip": club.name,
        tabIndex: 0,
        children: /* @__PURE__ */ jsx("h2", { className: "card-title line-clamp-1 max-w-xs", children: club.name })
      }
    ) })
  ] });
}
function FilterSelectCollapse({
  title,
  placeholder,
  options = {},
  selectedOption = null,
  onChange,
  isMulti = false
}) {
  const [isOpen, setIsOpen] = useState(selectedOption.length);
  const selectOptions = Object.entries(options).map(([value, label]) => ({
    value,
    label
  }));
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: `collapse collapse-plus ${isOpen ? "collapse-open" : ""}`,
      children: [
        /* @__PURE__ */ jsx(
          "div",
          {
            className: "collapse-title font-semibold flex justify-between items-center cursor-pointer",
            onClick: () => setIsOpen(!isOpen),
            children: /* @__PURE__ */ jsxs("span", { children: [
              title,
              Array.isArray(selectedOption) && selectedOption.length > 0 && /* @__PURE__ */ jsxs("span", { className: "ml-2 text-xs text-primary", children: [
                "(",
                selectedOption.length,
                ")"
              ] })
            ] })
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "collapse-content", children: /* @__PURE__ */ jsx(
          SelectInput,
          {
            options: selectOptions,
            value: isMulti ? selectOptions.filter(
              (opt) => selectedOption == null ? void 0 : selectedOption.includes(opt.value)
            ) : selectOptions.find(
              (opt) => opt.value === selectedOption
            ),
            onChange: (selected) => {
              if (isMulti) {
                onChange(selected.map((item) => item.value));
              } else {
                onChange((selected == null ? void 0 : selected.value) || null);
              }
            },
            isMulti,
            placeholder,
            menuPortalTarget: document.body
          }
        ) }),
        /* @__PURE__ */ jsx("div", { className: "divider m-0" })
      ]
    }
  );
}
const fetchClubs = createAsyncThunk(
  "clubs/fetchClubs",
  async ({ url, filters, canAppendClubs = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendClubs, ...response.data };
    } else {
      throw new Error("Failed to fetch clubs");
    }
  }
);
const fetchFilterOptions$3 = createAsyncThunk(
  "clubs/fetchFilterOptions",
  async () => {
    const response = await axios$1.get(route("api.clubs.filters"));
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error("Failed to fetch filter options");
    }
  }
);
const initialState$d = {
  clubs: [],
  loading: true,
  nextPageUrl: null,
  filterOptions: {},
  filterOptionsLoading: false,
  isFilterOptionsInitialized: false,
  filters: {
    stadiums: [],
    countries: [],
    search: "",
    sort: ""
  }
};
const clubsSlice = createSlice({
  name: "clubs",
  initialState: initialState$d,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$d.filters;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    resetNextPageUrl: (state) => {
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchClubs.pending, (state) => {
      state.loading = true;
    }).addCase(fetchClubs.fulfilled, (state, action) => {
      const { clubs, meta } = action.payload;
      if (action.payload.canAppendClubs) {
        const existingClubsMap = new Map(
          state.clubs.map((club) => [club.id, club])
        );
        const newClubs = clubs.filter(
          (club) => !existingClubsMap.has(club.id)
        );
        state.clubs = [...state.clubs, ...newClubs];
      } else {
        state.clubs = clubs;
      }
      state.nextPageUrl = meta.next_page_url;
      state.loading = false;
    }).addCase(fetchClubs.rejected, (state) => {
      state.clubs = [];
      state.loading = false;
    });
    builder.addCase(fetchFilterOptions$3.pending, (state) => {
      state.filterOptionsLoading = true;
    }).addCase(fetchFilterOptions$3.fulfilled, (state, action) => {
      state.filterOptions = action.payload;
      state.filterOptionsLoading = false;
      state.isFilterOptionsInitialized = true;
    }).addCase(fetchFilterOptions$3.rejected, (state) => {
      state.filterOptions = {};
      state.filterOptionsLoading = false;
    });
  }
});
const { resetFilters: resetFilters$6, setFilter: setFilter$5, resetNextPageUrl: resetNextPageUrl$5 } = clubsSlice.actions;
const clubsReducer = clubsSlice.reducer;
function useClubs() {
  const {
    clubs,
    loading,
    filters,
    filterOptions,
    nextPageUrl,
    isFilterOptionsInitialized
  } = useSelector((state) => state.clubs);
  const dispatch = useDispatch();
  const updateFilter = (key, value) => {
    dispatch(setFilter$5({ key, value }));
  };
  const clearFilters = useCallback(() => {
    dispatch(resetFilters$6());
  }, []);
  const fetchOptions = () => {
    if (isFilterOptionsInitialized) {
      return;
    }
    dispatch(fetchFilterOptions$3());
  };
  const fetchClubsInitially = () => {
    if (isFilterOptionsInitialized > 0) {
      return;
    }
    dispatch(
      fetchClubs({
        url: route("api.clubs.index"),
        filters
      })
    );
  };
  const refreshClubs = () => {
    dispatch(
      fetchClubs({
        url: route("api.clubs.index"),
        filters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl$5());
  };
  const loadMoreClubs = () => {
    if (!nextPageUrl) {
      return;
    }
    return dispatch(
      fetchClubs({
        url: nextPageUrl,
        filters,
        canAppendClubs: true
      })
    );
  };
  return {
    clubs,
    loading,
    filterOptions,
    filters,
    updateFilter,
    clearFilters,
    fetchOptions,
    fetchClubsInitially,
    refreshClubs,
    nextPageUrl,
    loadMoreClubs,
    clearNextPageUrl
  };
}
function ClubSidebar() {
  const { translate } = useTranslations();
  const { filters, clearFilters, updateFilter, filterOptions, refreshClubs } = useClubs();
  const debouncedFilters = useDebounce(filters, 800);
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    refreshClubs();
  }, [debouncedFilters]);
  return /* @__PURE__ */ jsxs("aside", { className: "md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3", children: [
    /* @__PURE__ */ jsx("h2", { className: "font-semibold mt-8 mb-4", children: translate("clubs.filters_title", "Filters") }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "text",
          placeholder: translate(
            "clubs.search_placeholder",
            "Search clubs..."
          ),
          className: "input input-bordered w-full",
          value: (filters == null ? void 0 : filters.search) || "",
          onChange: (e) => updateFilter("search", e.target.value)
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: clearFilters,
          className: "text-gray-500 hover:text-gray-800 transition-colors",
          title: translate("clubs.reset_filters", "Reset Filters"),
          children: /* @__PURE__ */ jsx(XCircle, { size: 24 })
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate("clubs.countries_title", "Countries"),
        placeholder: translate(
          "clubs.countries_placeholder",
          "Select countries"
        ),
        options: filterOptions["countries"],
        selectedOption: filters["countries"],
        onChange: (value) => {
          updateFilter("countries", value);
        },
        isMulti: true
      },
      "countries"
    ),
    /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate("clubs.stadiums_title", "Stadiums"),
        placeholder: translate(
          "clubs.stadiums_placeholder",
          "Select stadiums"
        ),
        options: filterOptions["stadiums"],
        selectedOption: filters["stadiums"],
        onChange: (value) => {
          updateFilter("stadiums", value);
        },
        isMulti: true
      },
      "stadiums"
    )
  ] });
}
function Clubs() {
  const { translate } = useTranslations();
  const {
    clubs,
    filters,
    updateFilter,
    fetchOptions,
    loading,
    loadMoreClubs,
    clearNextPageUrl,
    nextPageUrl,
    fetchClubsInitially
  } = useClubs();
  useEffect(() => {
    clearNextPageUrl();
    fetchOptions();
    const timer = setTimeout(() => {
      fetchClubsInitially();
    }, 10);
    return () => clearTimeout(timer);
  }, []);
  const [observerRef] = useInfiniteScroll({
    loading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreClubs,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("clubs.head_title", "Clubs") }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8", children: [
      /* @__PURE__ */ jsx(ClubSidebar, {}),
      /* @__PURE__ */ jsxs("main", { className: "md:w-3/4 w-full", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-8", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-2xl md:text-4xl font-bold", children: translate("clubs.page_title", "All Clubs") }),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              wrapperClass: "w-1/2 md:w-1/3",
              options: translate("clubs.sort_options"),
              value: filters.sort ? translate("clubs.sort_options").find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (selected) => updateFilter("sort", selected.value),
              placeholder: translate("clubs.sort_by_placeholder")
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "h-[800px] overflow-y-auto my-5", children: !loading && clubs.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate(
            "clubs.no_clubs",
            "Sorry, no clubs match your filters."
          ) }),
          /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("clubs.no_clubs_details") })
        ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: clubs.map((club, index) => /* @__PURE__ */ jsx(
            Link,
            {
              href: route(
                "detail.show",
                club.slug
              ),
              className: "hover:scale-[1.02] transition-transform duration-150",
              children: /* @__PURE__ */ jsx(ClubCard, { club })
            },
            `${club.id}-${index}`
          )) }),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          ),
          loading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
        ] }) })
      ] })
    ] })
  ] });
}
Clubs.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_9 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Clubs
}, Symbol.toStringTag, { value: "Module" }));
function EventCard({
  event,
  translationFrom,
  translationBuyTickets
}) {
  const goToEvent = () => {
    router.visit(route("detail.show", event.slug));
  };
  const goToStadium = (e) => {
    e.stopPropagation();
    router.visit(route("detail.show", event.stadium.slug));
  };
  const goToLeague = (e) => {
    e.stopPropagation();
    router.visit(route("detail.show", event.league.slug));
  };
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: "card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow",
      onClick: goToEvent,
      children: [
        /* @__PURE__ */ jsxs("figure", { className: "relative h-48 bg-gray-200", children: [
          event.image !== "" ? /* @__PURE__ */ jsx(
            "img",
            {
              src: event.image,
              alt: event.image_alt || event.name,
              className: "w-full h-full object-cover"
            }
          ) : /* @__PURE__ */ jsx("img", { src: "/img/ticketgol-logo.png", alt: event.name }),
          /* @__PURE__ */ jsxs("div", { className: "absolute top-4 right-4 badge badge-warning", children: [
            translationFrom,
            " €",
            event.min_price
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "card-body p-5", children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "tooltip tooltip-neutral w-fit text-left",
              "data-tip": event.name,
              tabIndex: 0,
              children: /* @__PURE__ */ jsx("h2", { className: "card-title line-clamp-1 max-w-xs", children: event.name })
            }
          ),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-gray-600", children: [
            /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsx("span", { className: "text-sm", children: dayjs(event.date).format("DD/MM/YYYY") })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-gray-600 hover:underline", children: [
            /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsx("span", { className: "text-sm truncate", onClick: goToStadium, children: event.stadium_name })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-gray-600 hover:underline", children: [
            /* @__PURE__ */ jsx(ShieldCheck, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsx("span", { className: "text-sm truncate", onClick: goToLeague, children: event.league_name })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "card-actions justify-end mt-2", children: /* @__PURE__ */ jsx("button", { className: "btn btn-primary btn-sm", children: translationBuyTickets }) })
        ] })
      ]
    }
  );
}
const fetchClubDetail = createAsyncThunk(
  "clubs/fetchClubDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.club;
    } else {
      throw new Error("Failed to fetch club detail");
    }
  }
);
const initialState$c = {
  club: null,
  clubLoading: true
};
const clubDetailSlice = createSlice({
  name: "club",
  initialState: initialState$c,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchClubDetail.pending, (state) => {
      state.clubLoading = true;
    }).addCase(fetchClubDetail.fulfilled, (state, action) => {
      state.club = action.payload;
      state.clubLoading = false;
    }).addCase(fetchClubDetail.rejected, (state) => {
      state.club = null;
      state.clubLoading = false;
    });
  }
});
const clubDetailReducer = clubDetailSlice.reducer;
function useClubDetail() {
  const { club, clubLoading } = useSelector((state) => state.club);
  const dispatch = useDispatch();
  const getClubDetail = (slug) => {
    dispatch(
      fetchClubDetail({ url: route("api.clubs.show", { slug }) })
    );
  };
  return {
    club,
    clubLoading,
    getClubDetail
  };
}
const fetchEvents = createAsyncThunk(
  "events/fetchEvents",
  async ({ url, filters, canAppendEvents = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendEvents, ...response.data };
    } else {
      throw new Error("Failed to fetch events");
    }
  }
);
const fetchFilterOptions$2 = createAsyncThunk(
  "events/fetchFilterOptions",
  async () => {
    const response = await axios$1.get(route("api.events.filters"));
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error("Failed to fetch filter options");
    }
  }
);
const initialState$b = {
  events: [],
  eventLoading: true,
  nextPageUrl: null,
  filterOptions: {},
  filterOptionsLoading: false,
  isFilterOptionsInitialized: false,
  isFilterCleared: false,
  filters: {
    categories: [],
    stadiums: [],
    clubs: [],
    countries: [],
    leagues: [],
    search: "",
    sort: ""
  }
};
const eventsSlice = createSlice({
  name: "events",
  initialState: initialState$b,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$b.filters;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setIsFilterCleared: (state, action) => {
      state.isFilterCleared = action.payload;
    },
    toggleCategories: (state, action) => {
      const { value } = action.payload;
      if (state.filters.categories.includes(value)) {
        state.filters.categories = state.filters.categories.filter(
          (item) => item !== value
        );
      } else {
        state.filters.categories.push(value);
      }
    },
    resetNextPageUrl: (state) => {
      state.events = [];
      state.eventLoading = true;
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchEvents.pending, (state) => {
      state.eventLoading = true;
    }).addCase(fetchEvents.fulfilled, (state, action) => {
      const { events, meta } = action.payload;
      if (action.payload.canAppendEvents) {
        const existingEventsMap = new Map(
          state.events.map((event) => [event.id, event])
        );
        const newEvents = events.filter(
          (event) => !existingEventsMap.has(event.id)
        );
        state.events = [...state.events, ...newEvents];
      } else {
        state.events = events;
      }
      state.nextPageUrl = meta.next_page_url;
      state.eventLoading = false;
    }).addCase(fetchEvents.rejected, (state) => {
      state.events = [];
      state.eventLoading = false;
    });
    builder.addCase(fetchFilterOptions$2.pending, (state) => {
      state.filterOptionsLoading = true;
    }).addCase(fetchFilterOptions$2.fulfilled, (state, action) => {
      state.filterOptions = action.payload;
      state.filterOptionsLoading = false;
      state.isFilterOptionsInitialized = true;
    }).addCase(fetchFilterOptions$2.rejected, (state) => {
      state.filterOptions = {};
      state.filterOptionsLoading = false;
    });
  }
});
const {
  resetFilters: resetFilters$5,
  setFilter: setFilter$4,
  toggleCategories,
  resetNextPageUrl: resetNextPageUrl$4,
  setIsFilterCleared
} = eventsSlice.actions;
const eventsReducer = eventsSlice.reducer;
function useEvents() {
  const {
    events,
    eventLoading,
    filters,
    filterOptions,
    nextPageUrl,
    isFilterOptionsInitialized,
    isFilterCleared
  } = useSelector((state) => state.events);
  const dispatch = useDispatch();
  const updateFilter = (key, value) => {
    dispatch(setFilter$4({ key, value }));
  };
  const updatesFilterCleared = (value) => {
    dispatch(setIsFilterCleared(value));
  };
  const updateMultipleFilters = (filtersObject) => {
    Object.entries(filtersObject).forEach(([key, value]) => {
      if (key === "categories") {
        value.forEach((category) => {
          dispatch(toggleCategories({ value: category }));
        });
        return;
      }
      dispatch(setFilter$4({ key, value }));
    });
  };
  const clearFilters = useCallback(() => {
    dispatch(resetFilters$5());
  }, []);
  const updateCategories = (value) => {
    dispatch(toggleCategories({ value }));
  };
  const fetchOptions = () => {
    if (isFilterOptionsInitialized) {
      return;
    }
    dispatch(fetchFilterOptions$2());
  };
  const fetchEventsInitially = () => {
    dispatch(
      fetchEvents({
        url: route("api.events.index"),
        filters
      })
    );
  };
  const refreshEvents = () => {
    dispatch(
      fetchEvents({
        url: route("api.events.index"),
        filters,
        updateMultipleFilters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl$4());
  };
  const loadMoreEvents = () => {
    if (!nextPageUrl) {
      return;
    }
    return dispatch(
      fetchEvents({
        url: nextPageUrl,
        filters,
        canAppendEvents: true
      })
    );
  };
  return {
    events,
    eventLoading,
    filterOptions,
    filters,
    updateFilter,
    updateMultipleFilters,
    clearFilters,
    updateCategories,
    fetchOptions,
    fetchEventsInitially,
    refreshEvents,
    nextPageUrl,
    loadMoreEvents,
    clearNextPageUrl,
    isFilterCleared,
    updatesFilterCleared
  };
}
function ClubEventList() {
  const { translate } = useTranslations();
  const { club } = useClubDetail();
  const {
    events,
    filters,
    updateFilter,
    eventLoading,
    loadMoreEvents,
    clearNextPageUrl,
    clearFilters,
    nextPageUrl,
    fetchEventsInitially
  } = useEvents();
  useEffect(() => {
    clearFilters();
    clearNextPageUrl();
    updateFilter("clubs", [club.id]);
  }, [club.id]);
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchEventsInitially();
    }, 50);
    return () => clearTimeout(timer);
  }, [filters]);
  const [observerRef] = useInfiniteScroll({
    loading: eventLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEvents,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col md:flex-row md:justify-between md:items-center gap-2 mb-2 mt-8", children: [
      /* @__PURE__ */ jsxs("h1", { className: "text-base lg:text-2xl md:text-lg font-bold", children: [
        translate("clubs.events_in_text"),
        " ",
        club.name
      ] }),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          wrapperClass: "w-full md:w-1/4",
          options: translate("clubs.event_sort_options"),
          value: filters.sort ? translate("clubs.event_sort_options").find(
            (option) => option.value === filters.sort
          ) : null,
          onChange: (selected) => updateFilter("sort", selected.value),
          placeholder: translate("clubs.sort_by_placeholder")
        }
      )
    ] }),
    !eventLoading && events.length === 0 ? /* @__PURE__ */ jsx("div", { className: "flex flex-col items-center justify-center px-5 h-96 bg-base-100 mt-5 rounded-xl shadow-sm", children: /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("clubs.no_events") }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("div", { className: "container mx-auto py-5 grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4", children: events.map((event, index) => /* @__PURE__ */ jsx(
        "div",
        {
          className: "hover:scale-[1.02] cursor-pointer transition-transform duration-150",
          children: /* @__PURE__ */ jsx(
            EventCard,
            {
              event,
              translationFrom: translate("common.from"),
              translationBuyTickets: translate(
                "common.buy_tickets"
              )
            }
          )
        },
        event.id
      )) }),
      nextPageUrl && /* @__PURE__ */ jsx("div", { ref: observerRef, className: "h-10" }),
      eventLoading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-32", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] })
  ] });
}
function Show$3() {
  const { slug } = usePage().props;
  const { translate } = useTranslations();
  const { club, getClubDetail, clubLoading } = useClubDetail();
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    getClubDetail(slug);
  }, []);
  if (clubLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: club.name }),
      /* @__PURE__ */ jsx("meta", { name: "title", content: club.meta_title }),
      /* @__PURE__ */ jsx("meta", { name: "keywords", content: club.meta_keywords }),
      /* @__PURE__ */ jsx("meta", { name: "description", content: club.meta_description })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-10 py-5", children: [
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        club.image !== "" ? /* @__PURE__ */ jsx(
          "img",
          {
            src: club.image,
            alt: club.image_alt || club.name,
            className: "w-full h-96 object-cover rounded-xl"
          }
        ) : /* @__PURE__ */ jsx("div", { className: "bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500", children: /* @__PURE__ */ jsx(
          "img",
          {
            src: "/img/ticketgol-logo.png",
            alt: club.name,
            className: "object-cover w-1/3"
          }
        ) }),
        /* @__PURE__ */ jsxs("div", { className: "absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-xl sm:text-3xl font-bold mb-2", children: club.name }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-warning font-medium", children: [
            /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsx("span", { className: "text-base sm:text-lg", children: club.country.name })
          ] }),
          /* @__PURE__ */ jsx("p", { className: "text-sm sm:text-base mt-2 max-w-xl", children: club.description })
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "mt-4", children: /* @__PURE__ */ jsxs(
        "div",
        {
          className: `collapse collapse-plus bg-base-100 border border-base-300 rounded-box 
                            ${isOpen ? "collapse-open" : ""}`,
          onClick: () => setIsOpen((prev) => !prev),
          children: [
            /* @__PURE__ */ jsx("div", { className: "collapse-title font-semibold flex justify-between items-center cursor-pointer", children: /* @__PURE__ */ jsxs("span", { children: [
              translate("clubs.about_text", "About the"),
              " ",
              club.name
            ] }) }),
            /* @__PURE__ */ jsx("div", { className: "collapse-content text-gray-700", children: /* @__PURE__ */ jsx(
              "div",
              {
                className: "prose",
                dangerouslySetInnerHTML: {
                  __html: club.detailed_description
                }
              }
            ) })
          ]
        }
      ) }),
      /* @__PURE__ */ jsx(ClubEventList, {})
    ] })
  ] });
}
Show$3.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_10 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Show$3
}, Symbol.toStringTag, { value: "Module" }));
function Dashboard$1() {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: "Dashboard" }),
    /* @__PURE__ */ jsx("div", { className: "py-12", children: /* @__PURE__ */ jsx("div", { className: "mx-auto max-w-7xl sm:px-6 lg:px-8", children: /* @__PURE__ */ jsx("div", { className: "overflow-hidden bg-white shadow-sm sm:rounded-lg", children: /* @__PURE__ */ jsx("div", { className: "p-6 text-gray-900", children: "You're logged in!" }) }) }) })
  ] });
}
Dashboard$1.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_11 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Dashboard$1
}, Symbol.toStringTag, { value: "Module" }));
function Error$1({ status }) {
  const { translate } = useTranslations();
  if (status === 404) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: translate("common.page_not_found") }),
      /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center py-12", children: /* @__PURE__ */ jsxs("div", { className: "text-center py-16 px-6 bg-white shadow-xl rounded-2xl max-w-lg", children: [
        /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsx(Ghost, { className: "w-16 h-16 text-primary animate-bounce" }) }),
        /* @__PURE__ */ jsx("h1", { className: "text-6xl font-extrabold text-gray-800 mb-4", children: "404" }),
        /* @__PURE__ */ jsx("p", { className: "text-xl text-gray-600 mb-6", children: translate("common.page_not_exist") }),
        /* @__PURE__ */ jsx(
          Link,
          {
            href: route("home"),
            className: "inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200",
            children: translate("common.back_home_btn")
          }
        )
      ] }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("common.error_title") }),
    /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center py-12", children: /* @__PURE__ */ jsxs("div", { className: "text-center px-6 py-16 bg-white shadow-xl rounded-2xl max-w-lg", children: [
      /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsx(AlertTriangle, { className: "w-16 h-16 text-yellow-500 animate-pulse" }) }),
      /* @__PURE__ */ jsx("h1", { className: "text-4xl font-bold text-gray-800 mb-2", children: translate("common.something_wrong") }),
      /* @__PURE__ */ jsx(
        Link,
        {
          href: route("home"),
          className: "inline-block bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition duration-200",
          children: translate("common.back_home_btn")
        }
      )
    ] }) })
  ] });
}
Error$1.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_12 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Error$1
}, Symbol.toStringTag, { value: "Module" }));
function getFilteredQueryParams(allowedKeys = [], clearParams = false) {
  const params = new URLSearchParams(window.location.search);
  const result = {};
  for (const [key, value] of params.entries()) {
    const baseKey = key.replace(/\[\d*\]$/, "");
    if (allowedKeys.length && !allowedKeys.includes(baseKey)) continue;
    if (key.includes("[")) {
      if (!result[baseKey]) {
        result[baseKey] = [];
      }
      result[baseKey].push(value);
    } else {
      result[key] = value;
    }
  }
  if (clearParams) {
    setTimeout(() => {
      window.history.replaceState(
        {},
        document.title,
        window.location.pathname
      );
    }, 500);
  }
  return result;
}
const eventsUrlParams = [
  "categories",
  "stadiums",
  "clubs",
  "countries",
  "leagues",
  "search",
  "sort",
  "page"
];
const selectFiltersConfig = [
  {
    key: "countries",
    titleKey: "countries_title",
    placeholderKey: "countries_placeholder"
  },
  {
    key: "stadiums",
    titleKey: "stadiums_title",
    placeholderKey: "stadiums_placeholder"
  },
  {
    key: "clubs",
    titleKey: "clubs_title",
    placeholderKey: "clubs_placeholder"
  },
  {
    key: "leagues",
    titleKey: "leagues_title",
    placeholderKey: "leagues_placeholder"
  }
];
function FilterCheckbox({ label, value, checked, onChange }) {
  return /* @__PURE__ */ jsxs("label", { className: "flex items-center gap-2 mb-2 cursor-pointer", children: [
    /* @__PURE__ */ jsx(
      "input",
      {
        type: "checkbox",
        className: "rounded border-gray-300 text-indigo-600 focus:ring-indigo-500",
        checked,
        onChange: () => onChange(value)
      }
    ),
    /* @__PURE__ */ jsx("span", { className: "text-sm text-gray-600", children: label })
  ] });
}
function FilterCheckboxCollapse({
  title,
  options = [],
  selectedOptions = [],
  onChange
}) {
  const [isOpen, setIsOpen] = useState(selectedOptions.length);
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: `collapse collapse-plus ${isOpen ? "collapse-open" : ""}`,
      children: [
        /* @__PURE__ */ jsx(
          "div",
          {
            className: "collapse-title font-semibold flex justify-between items-center cursor-pointer",
            onClick: () => setIsOpen(!isOpen),
            children: /* @__PURE__ */ jsxs("span", { children: [
              title,
              Array.isArray(selectedOptions) && selectedOptions.length > 0 && /* @__PURE__ */ jsxs("span", { className: "ml-2 text-xs text-primary", children: [
                "(",
                selectedOptions.length,
                ")"
              ] })
            ] })
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "collapse-content text-sm", children: Object.keys(options).length > 0 && Object.keys(options).map((key) => /* @__PURE__ */ jsx(
          FilterCheckbox,
          {
            label: options[key],
            value: key,
            checked: selectedOptions.includes(key),
            onChange
          },
          key
        )) }),
        /* @__PURE__ */ jsx("div", { className: "divider m-0" })
      ]
    }
  );
}
function EventSidebar() {
  const { translate } = useTranslations();
  const {
    filters,
    clearFilters,
    updateFilter,
    filterOptions,
    updateCategories,
    refreshEvents,
    isFilterCleared,
    updatesFilterCleared
  } = useEvents();
  const debouncedFilters = useDebounce(filters, 800);
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    if (isFilterCleared) {
      updatesFilterCleared(false);
      return;
    }
    refreshEvents();
  }, [debouncedFilters]);
  return /* @__PURE__ */ jsxs("aside", { className: "md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3", children: [
    /* @__PURE__ */ jsx("h2", { className: "font-semibold mt-8 mb-4", children: translate("events.filters_title") }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "text",
          placeholder: translate("events.search_placeholder"),
          className: "input input-bordered w-full",
          value: (filters == null ? void 0 : filters.search) || "",
          onChange: (e) => updateFilter("search", e.target.value)
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: clearFilters,
          className: "text-gray-500 hover:text-gray-800 transition-colors",
          title: translate("events.reset_filters"),
          children: /* @__PURE__ */ jsx(XCircle, { size: 24 })
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsx(
      FilterCheckboxCollapse,
      {
        title: translate("events.categories_title"),
        options: filterOptions.categories,
        selectedOptions: filters.categories,
        onChange: (value) => {
          updateCategories(value);
        }
      }
    ),
    selectFiltersConfig.map(({ key, titleKey, placeholderKey }) => /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate(`events.${titleKey}`),
        placeholder: translate(`events.${placeholderKey}`),
        options: filterOptions[key],
        selectedOption: filters[key],
        onChange: (value) => {
          updateFilter(key, value);
        },
        isMulti: true
      },
      key
    ))
  ] });
}
function Events() {
  const { translate } = useTranslations();
  const {
    events,
    filters,
    updateFilter,
    updateMultipleFilters,
    fetchOptions,
    eventLoading,
    loadMoreEvents,
    clearNextPageUrl,
    clearFilters,
    nextPageUrl,
    fetchEventsInitially,
    updatesFilterCleared
  } = useEvents();
  useEffect(() => {
    updatesFilterCleared(true);
    clearNextPageUrl();
    clearFilters();
  }, []);
  useEffect(() => {
    fetchOptions();
    const urlFilters = getFilteredQueryParams(eventsUrlParams, true);
    const timer = setTimeout(() => {
      if (Object.keys(urlFilters).length > 0) {
        updateMultipleFilters(urlFilters);
      } else {
        fetchEventsInitially();
      }
    }, 50);
    return () => clearTimeout(timer);
  }, [filters]);
  const [observerRef] = useInfiniteScroll({
    loading: eventLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEvents,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("events.head_title", "Events") }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8", children: [
      /* @__PURE__ */ jsx(EventSidebar, {}),
      /* @__PURE__ */ jsxs("main", { className: "md:w-3/4 w-full", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-8", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-2xl md:text-4xl font-bold", children: translate("events.page_title") }),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              wrapperClass: "w-1/2 md:w-1/3",
              options: translate("events.sort_options"),
              value: filters.sort ? translate("events.sort_options").find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (selected) => updateFilter("sort", selected.value),
              placeholder: translate(
                "events.sort_by_placeholder"
              )
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "h-[800px] overflow-y-auto my-5", children: !eventLoading && events.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("events.no_events") }),
          /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("events.no_events_details") })
        ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: events.map((event, index) => /* @__PURE__ */ jsx(
            "div",
            {
              className: "hover:scale-[1.02] cursor-pointer transition-transform duration-150",
              children: /* @__PURE__ */ jsx(
                EventCard,
                {
                  event,
                  translationFrom: translate(
                    "common.from"
                  ),
                  translationBuyTickets: translate(
                    "common.buy_tickets"
                  )
                }
              )
            },
            event.id
          )) }),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          ),
          eventLoading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
        ] }) })
      ] })
    ] })
  ] });
}
Events.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_13 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Events
}, Symbol.toStringTag, { value: "Module" }));
function SellTicketEventCard({ event, buttonTitle }) {
  return /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-md border border-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body p-5", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center gap-2 border-b pb-2", children: [
      /* @__PURE__ */ jsx(
        "div",
        {
          className: "tooltip tooltip-bottom tooltip-neutral w-full text-left",
          "data-tip": event.name,
          tabIndex: 0,
          children: /* @__PURE__ */ jsx("h2", { className: "card-title line-clamp-1 max-w-full", children: event.name })
        }
      ),
      /* @__PURE__ */ jsx(
        Link,
        {
          className: "btn btn-primary btn-outline btn-sm shrink-0 whitespace-nowrap",
          href: route("ticket.sell", event.slug),
          children: buttonTitle
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [
      /* @__PURE__ */ jsx("figure", { className: "relative h-20 bg-gray-200 object-cover rounded border border-base-300", children: event.image !== "" ? /* @__PURE__ */ jsx(
        "img",
        {
          src: event.image,
          alt: event.image_alt || event.name,
          className: "w-24 h-full object-cover"
        }
      ) : /* @__PURE__ */ jsx(
        "img",
        {
          src: "/img/ticketgol-logo.png",
          alt: event.name,
          className: "w-24"
        }
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center gap-1 text-gray-600 sm:max-w-[calc(100%-6rem)]", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4 mr-2" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: dayjs(event.date).format("DD/MM/YYYY") })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: event.stadium_name })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsx(ShieldCheck, { className: "w-4 h-4 mr-2" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: event.league_name })
        ] })
      ] })
    ] })
  ] }) });
}
const fetchEventsList = createAsyncThunk(
  "sellTickets/fetchEventsList",
  async ({ url, filters, canAppendEvents = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendEvents, ...response.data };
    } else {
      throw new Error("Failed to fetch events");
    }
  }
);
const initialState$a = {
  events: [],
  eventLoading: true,
  filterChanged: false,
  nextPageUrl: null,
  filters: {
    search: "",
    sort: ""
  }
};
const sellTicketsSlice = createSlice({
  name: "sellTickets",
  initialState: initialState$a,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$a.filters;
      state.filterChanged = true;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
      state.filterChanged = true;
    },
    resetNextPageUrl: (state) => {
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchEventsList.pending, (state) => {
      state.eventLoading = true;
    }).addCase(fetchEventsList.fulfilled, (state, action) => {
      const { events, meta } = action.payload;
      if (action.payload.canAppendEvents) {
        const existingEventsMap = new Map(
          state.events.map((event) => [event.id, event])
        );
        const newEvents = events.filter(
          (event) => !existingEventsMap.has(event.id)
        );
        state.events = [...state.events, ...newEvents];
      } else {
        state.events = events;
      }
      state.filterChanged = false;
      state.nextPageUrl = meta.next_page_url;
      state.eventLoading = false;
    }).addCase(fetchEventsList.rejected, (state) => {
      state.events = [];
      state.filterChanged = false;
      state.eventLoading = false;
    });
  }
});
const { resetFilters: resetFilters$4, setFilter: setFilter$3, resetNextPageUrl: resetNextPageUrl$3 } = sellTicketsSlice.actions;
const sellTicketsReducer = sellTicketsSlice.reducer;
function useSellTickets() {
  const { events, eventLoading, filters, filterChanged, nextPageUrl } = useSelector((state) => state.sellTickets);
  const dispatch = useDispatch();
  const updateFilter = (key, value) => {
    dispatch(setFilter$3({ key, value }));
  };
  const clearFilters = useCallback(() => {
    dispatch(resetFilters$4());
  }, []);
  const getEventsList = () => {
    dispatch(
      fetchEventsList({
        url: route("api.events.selltickets"),
        filters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl$3());
  };
  const loadMoreEvents = () => {
    if (!nextPageUrl) {
      return;
    }
    return dispatch(
      fetchEventsList({
        url: nextPageUrl,
        filters,
        canAppendEvents: true
      })
    );
  };
  return {
    events,
    eventLoading,
    filters,
    filterChanged,
    updateFilter,
    clearFilters,
    getEventsList,
    nextPageUrl,
    loadMoreEvents,
    clearNextPageUrl
  };
}
function SellTickets() {
  const { translate } = useTranslations();
  const {
    events,
    eventLoading,
    getEventsList,
    nextPageUrl,
    filters,
    filterChanged,
    updateFilter,
    clearFilters,
    loadMoreEvents
  } = useSellTickets();
  const debouncedFilters = useDebounce(filters, 500);
  const isFirstRender = useRef(true);
  useEffect(() => {
    getEventsList();
  }, []);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    getEventsList();
  }, [debouncedFilters]);
  const [observerRef] = useInfiniteScroll({
    loading: eventLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEvents,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      Head,
      {
        title: translate(
          "events.sell_tickets_page_title",
          "Sell Tickets"
        )
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "container px-4 py-8 mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-4xl", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center mb-6", children: [
        /* @__PURE__ */ jsx("h1", { className: "text-2xl font-bold", children: translate("events.sell_tickets_page_title") }),
        /* @__PURE__ */ jsx("p", { children: translate("events.sell_tickets_page_description") })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "bg-white p-5 shadow rounded-xl", children: [
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "relative w-full", children: [
            /* @__PURE__ */ jsx(Search$1, { className: "absolute left-3 top-1/2 -translate-y-1/2 mt-1 text-gray-400 w-5 h-5" }),
            /* @__PURE__ */ jsx(
              TextInput,
              {
                value: filters.search,
                onChange: (e) => updateFilter("search", e.target.value),
                placeholder: translate(
                  "events.sell_tickets_page_search_placeholder"
                ),
                className: "pl-10"
              }
            )
          ] }),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              options: translate(
                "events.sell_tickets_sort_options"
              ),
              value: filters.sort ? translate(
                "events.sell_tickets_sort_options"
              ).find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (option) => updateFilter("sort", (option == null ? void 0 : option.value) || ""),
              placeholder: translate(
                "my_tickets.sort_by_placeholder"
              )
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "flex gap-6 justify-end mt-2", children: /* @__PURE__ */ jsx(
          "button",
          {
            onClick: clearFilters,
            className: "btn btn-outline btn-sm",
            children: translate("common.clear", "Clear")
          }
        ) })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-4 max-h-[70vh] overflow-y-auto my-5", children: !eventLoading && events.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("events.no_events") }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("events.no_events_details") })
      ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
        filterChanged ? /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          events.map((event) => /* @__PURE__ */ jsx(
            SellTicketEventCard,
            {
              event,
              buttonTitle: translate(
                "events.sell_tickets_btn"
              )
            },
            event.id
          )),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          )
        ] }),
        eventLoading && !filterChanged && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
      ] }) })
    ] }) })
  ] });
}
SellTickets.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_14 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: SellTickets
}, Symbol.toStringTag, { value: "Module" }));
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);
function EventDateTime({ event }) {
  const { translate } = useTranslations();
  const getAbbreviation = (date) => {
    const fullTzName = date.format("zzz");
    return fullTzName.split(" ").map((word) => word[0]).join("");
  };
  const userTimezone = dayjs.tz.guess();
  const timeInBaseTimezone = dayjs.tz(event.time, event.timezone);
  const timeInUserTimezone = timeInBaseTimezone.clone().tz(userTimezone);
  const baseAbbr = getAbbreviation(timeInBaseTimezone);
  const userAbbr = getAbbreviation(timeInUserTimezone);
  const isSameTz = baseAbbr === userAbbr;
  return /* @__PURE__ */ jsxs("span", { children: [
    /* @__PURE__ */ jsxs("b", { children: [
      dayjs(event.date).format("DD/MM/YYYY"),
      " |",
      " ",
      timeInUserTimezone.format("h:mm A"),
      " ",
      userAbbr
    ] }),
    !isSameTz && /* @__PURE__ */ jsxs("i", { children: [
      " ",
      "(",
      timeInBaseTimezone.format("h:mm A"),
      " ",
      baseAbbr,
      " ",
      translate("common.local", "Local"),
      ")"
    ] })
  ] });
}
function EventDetailSection({ event }) {
  return /* @__PURE__ */ jsxs("div", { className: "px-5 mb-5", children: [
    /* @__PURE__ */ jsxs("h2", { className: "mt-5 text-2xl font-bold leading-tight", children: [
      event.name,
      /* @__PURE__ */ jsx("span", { className: "ml-4 badge badge-dash badge-neutral badge-outline badge-lg", children: event.category.label })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(EventDateTime, { event })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline", children: [
      /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(Link, { href: route("detail.show", event.stadium.slug), children: /* @__PURE__ */ jsxs("span", { children: [
        /* @__PURE__ */ jsxs("b", { children: [
          event.stadium.name,
          ","
        ] }),
        " ",
        event.stadium.address_line_1,
        ",",
        " ",
        event.stadium.address_line_2,
        ",",
        " ",
        event.stadium.country.name,
        ", ",
        event.stadium.postcode
      ] }) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline", children: [
      /* @__PURE__ */ jsx(ShieldCheck, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(Link, { href: route("detail.show", event.league.slug), children: /* @__PURE__ */ jsx("span", { className: "font-bold", children: event.league.name }) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline", children: [
      /* @__PURE__ */ jsx(House, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(Link, { href: route("detail.show", event.home_club.slug), children: /* @__PURE__ */ jsx("span", { className: "font-bold", children: event.home_club.name }) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline", children: [
      /* @__PURE__ */ jsx(HousePlus, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(Link, { href: route("detail.show", event.guest_club.slug), children: /* @__PURE__ */ jsx("span", { className: "font-bold", children: event.guest_club.name }) })
    ] })
  ] });
}
const fetchEventDetail$1 = createAsyncThunk(
  "events/fetchEventDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.event;
    } else {
      throw new Error("Failed to fetch event detail");
    }
  }
);
const fetchEventTickets = createAsyncThunk(
  "events/fetchEventTickets",
  async ({ url, eventId, filters, canAppendTickets = false }) => {
    const response = await axios$1.post(url, {
      ...filters,
      eventId
    });
    if (response.data.success === true) {
      return { canAppendTickets, ...response.data };
    } else {
      throw new Error("Failed to fetch tickets");
    }
  }
);
const initialState$9 = {
  event: null,
  eventLoading: true,
  tickets: [],
  nextPageUrl: null,
  ticketsLoading: false,
  totalTickets: 0,
  originalPriceRange: [],
  filters: {
    priceRange: [],
    sector: "",
    quantity: "",
    ticketType: "",
    sort: "remain_qty_asc"
  }
};
const eventDetailSlice = createSlice({
  name: "event",
  initialState: initialState$9,
  reducers: {
    setTicketFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    resetTicketFilters: (state) => {
      state.filters = {
        ...initialState$9.filters,
        priceRange: state.originalPriceRange
      };
    },
    resetNextPageUrl: (state) => {
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchEventDetail$1.pending, (state) => {
      state.eventLoading = true;
    }).addCase(fetchEventDetail$1.fulfilled, (state, action) => {
      state.event = action.payload;
      state.eventLoading = false;
      const minPrice = state.event.min_price === state.event.max_price ? 0 : state.event.min_price;
      const range = [minPrice, state.event.max_price];
      state.filters.priceRange = range;
      state.originalPriceRange = range;
    }).addCase(fetchEventDetail$1.rejected, (state) => {
      state.event = null;
      state.eventLoading = false;
    });
    builder.addCase(fetchEventTickets.pending, (state) => {
      state.ticketsLoading = true;
    }).addCase(fetchEventTickets.fulfilled, (state, action) => {
      const { tickets, meta } = action.payload;
      if (action.payload.canAppendTickets) {
        const existingTicketsMap = new Map(
          state.tickets.map((ticket) => [ticket.id, ticket])
        );
        const newTickets = tickets.filter(
          (ticket) => !existingTicketsMap.has(ticket.id)
        );
        state.tickets = [...state.tickets, ...newTickets];
      } else {
        state.tickets = tickets;
      }
      state.nextPageUrl = meta.next_page_url;
      state.totalTickets = meta.total;
      state.ticketsLoading = false;
    }).addCase(fetchEventTickets.rejected, (state) => {
      state.tickets = [];
      state.ticketsLoading = false;
      state.totalTickets = 0;
    });
  }
});
const { setTicketFilter, resetTicketFilters, resetNextPageUrl: resetNextPageUrl$2 } = eventDetailSlice.actions;
const eventDetailReducer = eventDetailSlice.reducer;
function useEventDetail() {
  const {
    event,
    eventLoading,
    tickets,
    ticketsLoading,
    totalTickets,
    filters,
    nextPageUrl
  } = useSelector((state) => state.event);
  const dispatch = useDispatch();
  const getEventDetail = (slug) => {
    dispatch(
      fetchEventDetail$1({ url: route("api.events.show", { slug }) })
    );
  };
  const getEventTickets = () => {
    dispatch(
      fetchEventTickets({
        url: route("api.tickets.index"),
        eventId: event.id,
        filters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl$2());
  };
  const loadMoreEventTickets = () => {
    if (!nextPageUrl) return Promise.resolve();
    return dispatch(
      fetchEventTickets({
        url: nextPageUrl,
        eventId: event.id,
        filters,
        canAppendTickets: true
      })
    ).unwrap();
  };
  const updateTicketFilter = (key, value) => {
    dispatch(setTicketFilter({ key, value }));
  };
  const clearTicketFilters = () => {
    dispatch(resetTicketFilters());
  };
  return {
    event,
    eventLoading,
    getEventDetail,
    tickets,
    ticketsLoading,
    getEventTickets,
    totalTickets,
    filters,
    nextPageUrl,
    loadMoreEventTickets,
    updateTicketFilter,
    clearTicketFilters,
    clearNextPageUrl
  };
}
function RangeSlider({
  label,
  values,
  min,
  max,
  step = 1,
  onChange
}) {
  const currentValuesRef = useRef(values);
  const debouncedOnChange = useMemo(() => {
    return debounce((v) => {
      onChange(v);
    }, 50);
  }, [onChange]);
  useEffect(() => {
    currentValuesRef.current = values;
  }, [values]);
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);
  return /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
    label && /* @__PURE__ */ jsx("label", { className: "label-text font-medium", children: label }),
    /* @__PURE__ */ jsx(
      Range,
      {
        values,
        step,
        min,
        max,
        onChange: (newValues) => {
          currentValuesRef.current = newValues;
          debouncedOnChange(newValues);
        },
        renderTrack: ({ props, children }) => {
          return /* @__PURE__ */ jsx(
            "div",
            {
              ...props,
              style: {
                ...props.style,
                height: "6px",
                width: "100%",
                background: getTrackBackground({
                  values,
                  colors: ["#d1d5db", "#3b82f6", "#d1d5db"],
                  min,
                  max
                }),
                borderRadius: "4px"
              },
              className: "mt-10",
              children
            }
          );
        },
        renderThumb: ({ props, index }) => {
          return /* @__PURE__ */ createElement(
            "div",
            {
              ...props,
              key: props.key,
              className: "relative flex flex-col items-center justify-center focus:outline-none",
              style: { ...props.style }
            },
            /* @__PURE__ */ jsxs(
              "div",
              {
                className: `absolute -top-9 px-2 py-1 rounded-md shadow text-sm bg-white text-gray-800 whitespace-nowrap z-10 ${props.key == 1 ? " -right-4" : " -left-4"}`,
                children: [
                  "€",
                  parseFloat(values[index]).toFixed(2)
                ]
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "h-4 w-4 rounded-full bg-blue-500 shadow-md" })
          );
        }
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between text-sm text-gray-600 mt-2", children: [
      /* @__PURE__ */ jsxs("span", { children: [
        "€",
        parseFloat(min).toFixed(2)
      ] }),
      /* @__PURE__ */ jsxs("span", { children: [
        "€",
        parseFloat(max).toFixed(2)
      ] })
    ] })
  ] });
}
function prepareOptionsFromEnum(enumObject = {}) {
  return Object.entries(enumObject).map(([value, label]) => ({
    value,
    label
  }));
}
function TicketFilters$1({ event }) {
  const { translate } = useTranslations();
  const { filters, updateTicketFilter, clearTicketFilters } = useEventDetail();
  const quantityOptions = [
    { label: translate("events.all_tickets"), value: "" },
    ...Array.from({ length: event.max_quantity }, (_, i) => ({
      label: `${i + 1} ${translate("events.tickets")}`,
      value: i + 1
    }))
  ];
  const sectorOptions = [
    { label: translate("events.all_sectors"), value: "" },
    ...prepareOptionsFromEnum(event.stadium_sectors)
  ];
  const ticketTypeOptions = [
    { label: translate("events.all_ticket_types"), value: "" },
    ...prepareOptionsFromEnum(translate("enums.ticket_types"))
  ];
  return /* @__PURE__ */ jsxs("div", { className: "relative bg-white p-4 shadow flex rounded-xl flex-wrap gap-4", children: [
    /* @__PURE__ */ jsx("div", { className: "w-full md:w-auto flex-1 min-w-[180px] pr-5", children: /* @__PURE__ */ jsx(
      RangeSlider,
      {
        values: filters.priceRange,
        onChange: (values) => updateTicketFilter("priceRange", values),
        min: 0,
        max: event.max_price,
        step: 1
      },
      0
    ) }),
    /* @__PURE__ */ jsx("div", { className: "w-full md:w-auto flex-1 min-w-[180px]", children: /* @__PURE__ */ jsx(
      SelectInput,
      {
        options: quantityOptions,
        value: quantityOptions.find(
          (opt) => opt.value === filters.quantity
        ),
        onChange: (option) => updateTicketFilter("quantity", (option == null ? void 0 : option.value) || ""),
        placeholder: translate("events.quantity_placeholder"),
        menuPortalTarget: document.body
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "w-full md:w-auto flex-1 min-w-[180px]", children: /* @__PURE__ */ jsx(
      SelectInput,
      {
        options: sectorOptions,
        value: sectorOptions.find(
          (opt) => opt.value === filters.sector
        ),
        onChange: (option) => updateTicketFilter("sector", (option == null ? void 0 : option.value) || ""),
        placeholder: translate("events.sector_placeholder"),
        menuPortalTarget: document.body
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "w-full md:w-auto flex-1 min-w-[180px]", children: /* @__PURE__ */ jsx(
      SelectInput,
      {
        options: ticketTypeOptions,
        value: ticketTypeOptions.find(
          (opt) => opt.value === filters.ticketType
        ),
        onChange: (option) => updateTicketFilter("ticketType", (option == null ? void 0 : option.value) || ""),
        placeholder: translate("events.ticket_type_placeholder"),
        menuPortalTarget: document.body
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "w-full md:w-auto flex-none", children: /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-primary mt-2",
        onClick: clearTicketFilters,
        children: translate("events.reset_filters")
      }
    ) })
  ] });
}
function TicketCard$1({ ticket, ticketsText, onBuyClick }) {
  return /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-sm border p-4 cursor-pointer", children: /* @__PURE__ */ jsxs(
    "div",
    {
      className: "flex justify-between items-center",
      onClick: () => onBuyClick(ticket),
      children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("p", { className: "font-medium mb-1", children: ticket.sector_name }),
          /* @__PURE__ */ jsxs("p", { className: "text-sm text-gray-500 mb-1", children: [
            ticket.remain_qty,
            " ",
            ticketsText
          ] }),
          /* @__PURE__ */ jsx(
            "p",
            {
              className: `badge badge-outline capitalize badge-${ticket.ticket_type.color}`,
              children: ticket.ticket_type.label
            }
          )
        ] }),
        /* @__PURE__ */ jsxs("p", { className: "text-lg font-bold", children: [
          "€",
          ticket.price
        ] })
      ]
    }
  ) });
}
function ConfirmPurchaseModal({
  open,
  onClose,
  onConfirm,
  btnDisabled,
  tempReservationMinutes
}) {
  const { translate } = useTranslations();
  const message = translate("events.confirm_purchase_desc").replace(
    "{{minutes}}",
    tempReservationMinutes
  );
  return /* @__PURE__ */ jsx("div", { className: `modal ${open ? "modal-open" : ""}`, role: "dialog", children: /* @__PURE__ */ jsxs("div", { className: "modal-box max-w-sm", children: [
    /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-4", children: /* @__PURE__ */ jsx(ClockAlert, { className: "w-12 h-12 text-primary" }) }),
    /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-center mb-2", children: translate("events.confirm_purchase_txt") }),
    /* @__PURE__ */ jsx("p", { className: "text-center mb-6", children: message }),
    /* @__PURE__ */ jsxs("div", { className: "modal-action flex justify-end gap-2", children: [
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: onClose,
          className: "btn btn-outline btn-sm",
          children: translate("common.cancel_btn")
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: onConfirm,
          disabled: btnDisabled,
          className: "btn btn-primary btn-sm",
          children: translate("events.proceed_btn")
        }
      )
    ] })
  ] }) });
}
function ActiveReservationModal({
  open,
  onClose,
  reservationId
}) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx("div", { className: `modal ${open ? "modal-open" : ""}`, role: "dialog", children: /* @__PURE__ */ jsxs("div", { className: "modal-box max-w-sm", children: [
    /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-sm btn-circle btn-ghost absolute right-2 top-2",
        onClick: onClose,
        children: "✕"
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-4", children: /* @__PURE__ */ jsx(Clock, { className: "w-12 h-12 text-primary" }) }),
    /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-center mb-2", children: translate("events.transaction_in_progress_title") }),
    /* @__PURE__ */ jsx("p", { className: "text-center mb-6", children: translate("events.transaction_in_progress_message") }),
    /* @__PURE__ */ jsx("div", { className: "modal-action flex justify-end gap-2", children: /* @__PURE__ */ jsx(
      Link,
      {
        href: route("ticket.checkout", reservationId),
        className: "btn btn-primary btn-sm",
        children: "Continue"
      }
    ) })
  ] }) });
}
function BuyTicketModal({ open, onClose, ticket }) {
  var _a, _b;
  const { auth } = usePage().props;
  const { event, filters } = useEventDetail();
  const { translate } = useTranslations();
  const isVisible = open && ticket;
  const ticketsOptions = ticket && event ? Array.from({ length: ticket.remain_qty }, (_, i) => {
    const quantity = i + 1;
    const remaining = ticket.remain_qty - quantity;
    const isValid = (() => {
      switch (ticket.quantity_split_type) {
        case event.quantitySplitEnums.SINGLE:
          return quantity === 1;
        case event.quantitySplitEnums.AVOID_ONE:
          return remaining !== 1;
        case event.quantitySplitEnums.AVOID_ODD:
          return remaining % 2 === 0;
        case event.quantitySplitEnums.AVOID_ONE_THREE:
          return remaining !== 1 && remaining !== 3;
        case event.quantitySplitEnums.ANY:
        default:
          return true;
      }
    })();
    return isValid ? {
      label: `${quantity} ${translate("events.tickets")}`,
      value: quantity
    } : null;
  }).filter(Boolean) : [];
  const defaultSelected = ((_a = ticketsOptions.find((opt) => opt.value === filters.quantity)) == null ? void 0 : _a.value) ?? ((_b = ticketsOptions.at(-1)) == null ? void 0 : _b.value) ?? null;
  const [purchaseQuantity, setPurchaseQuantity] = useState(defaultSelected);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showActiveReservationModal, setShowActiveReservationModal] = useState(false);
  const [reservationId, setReservationId] = useState(null);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  useEffect(() => {
    setPurchaseQuantity(defaultSelected);
    setFormErrors({});
  }, [defaultSelected]);
  const handlePurchaseQuantityChange = (e) => {
    setPurchaseQuantity(parseInt(e.value));
  };
  const handleBuyNowClick = async () => {
    if (!auth.user) {
      return router.visit(
        route("login", { redirect: window.location.pathname })
      );
    }
    try {
      const { data } = await axios$1.get(
        route("api.reservations.check-active")
      );
      if (data.success) {
        if (data.is_active) {
          setShowActiveReservationModal(true);
          setReservationId(data.reservationId);
        } else {
          setShowConfirmModal(true);
        }
      }
    } catch (error) {
      toast.error(translate("common.something_wrong"));
    }
  };
  const handleConfirmPurchase = async () => {
    var _a2, _b2, _c, _d;
    const params = {
      ticket_id: ticket.id,
      quantity: purchaseQuantity,
      price: ticket.price
    };
    setBtnDisabled(true);
    try {
      const { data } = await axios$1.post(
        route("api.reservations.create"),
        params
      );
      if (data.success) {
        router.visit(
          route("ticket.checkout", data.temp_ticket_reservation_id)
        );
      }
    } catch (error) {
      setBtnDisabled(false);
      if (((_a2 = error.response) == null ? void 0 : _a2.status) === 422 && ((_b2 = error.response.data) == null ? void 0 : _b2.errors)) {
        setFormErrors((_c = error.response.data) == null ? void 0 : _c.errors);
      } else if (((_d = error.response) == null ? void 0 : _d.status) === 400) {
        toast.error(error.response.data.message);
      } else {
        toast.error(translate("common.something_wrong"));
      }
      setShowConfirmModal(false);
    }
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("div", { className: `modal ${isVisible ? "modal-open" : ""}`, children: /* @__PURE__ */ jsx("div", { className: "modal-box max-w-sm", children: ticket && /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-bold", children: ticket.sector_name }),
      /* @__PURE__ */ jsx(
        "p",
        {
          className: `badge badge-outline capitalize badge-${ticket.ticket_type.color}`,
          children: ticket.ticket_type.label
        }
      ),
      /* @__PURE__ */ jsx("div", { className: "divider" }),
      /* @__PURE__ */ jsxs("div", { className: "mb-4", children: [
        /* @__PURE__ */ jsx("p", { className: "mb-2 font-semibold", children: translate("events.ticket_price_text") }),
        /* @__PURE__ */ jsxs("p", { className: "mb-4 text-cyan-600", children: [
          "€",
          ticket.price
        ] }),
        /* @__PURE__ */ jsx(
          SelectInput,
          {
            label: translate(
              "events.quantity_placeholder"
            ),
            options: ticketsOptions,
            value: ticketsOptions.find(
              (option) => option.value === purchaseQuantity
            ),
            onChange: handlePurchaseQuantityChange,
            error: formErrors == null ? void 0 : formErrors.quantity,
            menuPortalTarget: document.body
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "modal-action flex justify-end gap-2", children: [
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: onClose,
            className: "btn btn-outline btn-sm",
            children: translate("common.cancel_btn")
          }
        ),
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: handleBuyNowClick,
            className: "btn btn-primary btn-sm",
            children: translate("events.buy_now")
          }
        )
      ] })
    ] }) }) }),
    /* @__PURE__ */ jsx(
      ConfirmPurchaseModal,
      {
        open: showConfirmModal,
        onClose: () => setShowConfirmModal(false),
        onConfirm: handleConfirmPurchase,
        btnDisabled,
        tempReservationMinutes: event.tempReservationMinutes || 15
      }
    ),
    reservationId && /* @__PURE__ */ jsx(
      ActiveReservationModal,
      {
        open: showActiveReservationModal,
        onClose: () => setShowActiveReservationModal(false),
        reservationId
      }
    )
  ] });
}
function TicketListingSidebar() {
  const { translate } = useTranslations();
  const { slug } = usePage().props;
  const [selectedTicket, setSelectedTicket] = useState(null);
  const {
    tickets,
    filters,
    getEventTickets,
    ticketsLoading,
    totalTickets,
    nextPageUrl,
    loadMoreEventTickets,
    updateTicketFilter,
    clearNextPageUrl
  } = useEventDetail();
  useEffect(() => {
    if (ticketsLoading) return;
    clearNextPageUrl();
    getEventTickets();
  }, [filters]);
  const [observerRef] = useInfiniteScroll({
    loading: ticketsLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEventTickets,
    rootMargin: "100px"
  });
  const handleBuyClick = (ticket) => {
    setSelectedTicket(ticket);
  };
  const closeBuyModal = () => {
    setSelectedTicket(null);
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("div", { className: "lg:w-2/5 w-full bg-base-100 shadow-md p-6 rounded-xl space-y-4 md:max-h-screen md:overflow-y-auto md:sticky md:top-[120px]", children: [
      /* @__PURE__ */ jsx("div", { className: "flex justify-end w-full", children: /* @__PURE__ */ jsx(
        Link,
        {
          href: route("ticket.sell", slug),
          className: "btn btn-outline btn-primary btn-sm float-right",
          children: translate("events.sell_tickets_btn", "Sell Tickets")
        }
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between w-full", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-medium mt-2", children: [
          translate("events.total_listing"),
          " : ",
          totalTickets
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 min-w-[200px]", children: [
          /* @__PURE__ */ jsx("span", { className: "whitespace-nowrap mt-2 max-xl:hidden", children: translate("events.sort_by_placeholder") }),
          /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsx(
            SelectInput,
            {
              options: translate(
                "events.tickets_sort_options"
              ),
              value: filters.sort ? translate(
                "events.tickets_sort_options"
              ).find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (selected) => updateTicketFilter("sort", selected.value),
              placeholder: "",
              menuPortalTarget: document.body
            }
          ) })
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-4 max-h-[600px] overflow-y-auto pr-2", children: !ticketsLoading && tickets.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("events.no_tickets") }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("events.no_tickets_details") })
      ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
        tickets.map((ticket) => /* @__PURE__ */ jsx(
          TicketCard$1,
          {
            ticket,
            ticketsText: translate("events.tickets"),
            onBuyClick: handleBuyClick
          },
          ticket.id
        )),
        nextPageUrl && /* @__PURE__ */ jsx("div", { ref: observerRef, className: "h-10" }),
        ticketsLoading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-32", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
      ] }) })
    ] }),
    /* @__PURE__ */ jsx(
      BuyTicketModal,
      {
        open: !!selectedTicket,
        onClose: closeBuyModal,
        ticket: selectedTicket
      }
    )
  ] });
}
function Show$2() {
  const { translate } = useTranslations();
  const { slug } = usePage().props;
  const { event, getEventDetail, eventLoading } = useEventDetail();
  useEffect(() => {
    getEventDetail(slug);
  }, []);
  if (eventLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: event.name }),
      /* @__PURE__ */ jsx("meta", { name: "title", content: event.meta_title }),
      /* @__PURE__ */ jsx("meta", { name: "keywords", content: event.meta_keywords }),
      /* @__PURE__ */ jsx("meta", { name: "description", content: event.meta_description })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-6 py-5", children: [
      /* @__PURE__ */ jsx("div", { className: "hidden lg:block sticky top-0 z-10 mb-5", children: /* @__PURE__ */ jsx(TicketFilters$1, { event }) }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:flex-row gap-8 items-start", children: [
        /* @__PURE__ */ jsxs("div", { className: "lg:w-3/5 w-full bg-base-100 rounded-xl overflow-hidden shadow", children: [
          /* @__PURE__ */ jsx(EventDetailSection, { event }),
          event.image !== "" ? /* @__PURE__ */ jsx(
            "img",
            {
              src: event.image,
              alt: event.image_alt || event.name,
              className: "w-full h-auto"
            }
          ) : /* @__PURE__ */ jsx("div", { className: "bg-gray-200 w-full h-64 flex items-center justify-center", children: /* @__PURE__ */ jsx(
            "img",
            {
              src: "/img/ticketgol-logo.png",
              alt: event.name
            }
          ) }),
          /* @__PURE__ */ jsxs("div", { className: "px-5 mb-5", children: [
            /* @__PURE__ */ jsxs("div", { className: "mt-10", children: [
              /* @__PURE__ */ jsx("h3", { className: "text-xl font-bold mb-2", children: translate("events.about_the_event") }),
              /* @__PURE__ */ jsx("p", { className: "text-gray-700 leading-relaxed", children: event.description })
            ] }),
            Object.entries(event.restrictions).length > 0 && /* @__PURE__ */ jsxs("div", { className: "mt-4 border-t pt-5", children: [
              /* @__PURE__ */ jsx("h3", { className: "text-xl font-bold mb-2", children: translate("events.restriction_text") }),
              /* @__PURE__ */ jsx("ul", { className: "list-disc leading-relaxed ml-5", children: Object.entries(event.restrictions).map(
                ([id, name]) => /* @__PURE__ */ jsx("li", { children: name }, id)
              ) })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "block lg:hidden w-full", children: /* @__PURE__ */ jsx(TicketFilters$1, { event }) }),
        /* @__PURE__ */ jsx(TicketListingSidebar, {})
      ] })
    ] })
  ] });
}
Show$2.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_15 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Show$2
}, Symbol.toStringTag, { value: "Module" }));
const EventCarousel = ({ events }) => {
  const { translate } = useTranslations();
  const containerRef = useRef(null);
  const trackRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visibleItems, setVisibleItems] = useState(4);
  const [slideWidth, setSlideWidth] = useState(0);
  const updateLayout = () => {
    const width = window.innerWidth;
    if (width >= 1280) setVisibleItems(4);
    else if (width >= 1024) setVisibleItems(3);
    else if (width >= 640) setVisibleItems(2);
    else setVisibleItems(1);
  };
  const calculateSlideWidth = () => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth;
      setSlideWidth(containerWidth / visibleItems);
    }
  };
  useEffect(() => {
    updateLayout();
    window.addEventListener("resize", updateLayout);
    return () => window.removeEventListener("resize", updateLayout);
  }, []);
  useEffect(() => {
    calculateSlideWidth();
  }, [visibleItems, events.length]);
  useEffect(() => {
    if (trackRef.current) {
      trackRef.current.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
    }
  }, [currentIndex, slideWidth]);
  const maxIndex = Math.max(events.length - visibleItems, 0);
  const next = () => {
    setCurrentIndex((prev2) => Math.min(prev2 + 1, maxIndex));
  };
  const prev = () => {
    setCurrentIndex((prev2) => Math.max(prev2 - 1, 0));
  };
  return /* @__PURE__ */ jsxs("div", { className: "relative w-full overflow-hidden", ref: containerRef, children: [
    /* @__PURE__ */ jsx(
      "div",
      {
        ref: trackRef,
        className: "flex transition-transform duration-500 ease-in-out",
        style: { width: `${events.length * slideWidth}px` },
        children: events.map((event, idx) => /* @__PURE__ */ jsx(
          "div",
          {
            style: { width: `${slideWidth}px` },
            className: "px-3 py-10 flex-shrink-0 hover:scale-[1.02] cursor-pointer transition-transform duration-150",
            children: /* @__PURE__ */ jsx(
              EventCard,
              {
                event,
                translationFrom: translate("common.from"),
                translationBuyTickets: translate(
                  "common.buy_tickets"
                )
              }
            )
          },
          event.id || idx
        ))
      }
    ),
    events.length > visibleItems && /* @__PURE__ */ jsxs(Fragment, { children: [
      currentIndex > 0 && /* @__PURE__ */ jsx("div", { className: "absolute inset-y-0 left-2 flex items-center z-50", children: /* @__PURE__ */ jsx("button", { onClick: prev, className: "btn btn-circle", children: /* @__PURE__ */ jsx(ChevronLeft, { className: "w-6 h-6" }) }) }),
      currentIndex < maxIndex && /* @__PURE__ */ jsx("div", { className: "absolute inset-y-0 right-2 flex items-center z-50", children: /* @__PURE__ */ jsx("button", { onClick: next, className: "btn btn-circle", children: /* @__PURE__ */ jsx(ChevronRight, { className: "w-6 h-6" }) }) })
    ] })
  ] });
};
const searchResultTypeIcon = (type) => {
  const icons = {
    event: /* @__PURE__ */ jsx(CalendarDays, { className: "w-5 h-5 inline-block mr-1" }),
    stadium: /* @__PURE__ */ jsx(MapPinned, { className: "w-5 h-5 inline-block mr-1" }),
    league: /* @__PURE__ */ jsx(ShieldCheck, { className: "w-5 h-5 inline-block mr-1" }),
    club: /* @__PURE__ */ jsx(House, { className: "w-5 h-5 inline-block mr-1" })
  };
  return icons[type.toLowerCase()] || null;
};
const highlight = (text, query) => {
  if (!query) return text;
  const regex = new RegExp(`(${query})`, "gi");
  return text.split(regex).map(
    (part, i) => part.toLowerCase() === query.toLowerCase() ? /* @__PURE__ */ jsx("span", { className: "font-semibold", children: part }, i) : part
  );
};
function SearchListResult({ result, query }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
    Link,
    {
      href: route(`detail.show`, result.localized_slug.slug),
      className: "flex items-center gap-3 p-3 hover:bg-gray-50 transition",
      children: [
        /* @__PURE__ */ jsx("span", { className: "text-gray-600", children: searchResultTypeIcon(result.type) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { className: "text-gray-800", children: highlight(result.name, query) }),
          /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-400 capitalize", children: result.type.toLowerCase() === "event" ? dayjs(result.date).format("DD/MM/YYYY") : translate(`common.search_types.${result.type}`) })
        ] })
      ]
    }
  ) });
}
function SearchBox() {
  const { translate } = useTranslations();
  const [query, setQuery] = useState("");
  const [results, setResults] = useState([]);
  const [total, setTotal] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const fetchSuggestions = async (search) => {
    if (!search) {
      setResults([]);
      setShowDropdown(false);
      setTotal(0);
      return;
    }
    try {
      const response = await axios$1.get(route("api.search.suggestions"), {
        params: { q: search }
      });
      setResults(response.data.suggestions.data);
      setTotal(response.data.suggestions.total);
      setShowDropdown(true);
    } catch (err) {
      console.error(err);
    }
  };
  const debouncedFetch = useCallback(debounce(fetchSuggestions, 300), []);
  const handleChange = (e) => {
    const val = e.target.value;
    setQuery(val);
    debouncedFetch(val);
  };
  return /* @__PURE__ */ jsxs("div", { className: "relative w-full max-w-2xl mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "join w-full", children: [
      /* @__PURE__ */ jsx(Search$1, { className: "absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" }),
      /* @__PURE__ */ jsx(
        "input",
        {
          value: query,
          onChange: handleChange,
          className: "input input-bordered pl-10 w-full text-gray-600",
          placeholder: translate("common.search_placeholder")
        }
      )
    ] }),
    showDropdown && /* @__PURE__ */ jsx("div", { className: "absolute mt-1 w-full bg-white border shadow rounded text-left max-h-[350px] overflow-y-auto", children: /* @__PURE__ */ jsx("ul", { className: "divide-y", children: results.length > 0 ? /* @__PURE__ */ jsxs(Fragment, { children: [
      results.map((suggestion) => /* @__PURE__ */ jsx(
        SearchListResult,
        {
          result: suggestion,
          query
        },
        `${suggestion.type}-${suggestion.id}`
      )),
      query.trim() && total > 10 && /* @__PURE__ */ jsx("li", { className: "p-3 text-blue-600 hover:bg-gray-100 cursor-pointer text-center font-medium", children: /* @__PURE__ */ jsx(
        Link,
        {
          href: route("search", {
            q: encodeURIComponent(query)
          }),
          children: translate(
            "common.view_all_results"
          )
        }
      ) })
    ] }) : /* @__PURE__ */ jsx("li", { className: "p-3", children: translate("common.no_search_results_found") }) }) })
  ] });
}
const homeTestimonials = [
  {
    name: "Marco R.",
    location: "Milan, Italy",
    comment: "Perfect experience from purchase to event. The tickets arrived on time and everything was exactly as described.",
    rating: 5
  },
  {
    name: "Sarah L.",
    location: "London, UK",
    comment: "Used TicketGol for Champions League tickets. The process was smooth and customer service was excellent.",
    rating: 5
  },
  {
    name: "Juan C.",
    location: "Madrid, Spain",
    comment: "Great platform for finding tickets to sold-out matches. Will definitely use again!",
    rating: 5
  }
];
function Home() {
  const { translate } = useTranslations();
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [featuredEvents, setFeaturedEvents] = useState([]);
  useEffect(() => {
    const getEvents = async () => {
      try {
        const response = await axios$1.get(route("api.home.index"));
        if (response.data.success) {
          setUpcomingEvents(response.data.upcoming.events);
          setFeaturedEvents(response.data.featured.events);
        }
      } catch (error) {
      }
    };
    getEvents();
  }, []);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("common.welcome") }),
    /* @__PURE__ */ jsx("div", { className: "flex-grow", children: /* @__PURE__ */ jsxs("div", { className: "min-h-screen", children: [
      /* @__PURE__ */ jsxs(
        "div",
        {
          className: "hero min-h-[600px] relative z-10",
          style: {
            backgroundImage: "url('/img/banner.png')",
            backgroundSize: "cover",
            backgroundPosition: "center"
          },
          children: [
            /* @__PURE__ */ jsx("div", { className: "hero-overlay bg-opacity-70" }),
            /* @__PURE__ */ jsx("div", { className: "hero-content text-center text-neutral-content", children: /* @__PURE__ */ jsxs("div", { className: "max-w-3xl", children: [
              /* @__PURE__ */ jsx("h1", { className: "mb-5 text-5xl font-bold text-white", children: translate("common.banner_title") }),
              /* @__PURE__ */ jsx("p", { className: "mb-8 text-lg", children: translate("common.banner_description") }),
              /* @__PURE__ */ jsx(SearchBox, {})
            ] }) })
          ]
        }
      ),
      upcomingEvents.length > 0 && /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-5 py-12", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold pl-3", children: translate("common.upcoming_events") }),
          /* @__PURE__ */ jsxs(
            Link,
            {
              href: route("events"),
              className: "btn btn-ghost gap-2",
              children: [
                translate("common.view_all_text"),
                " ",
                /* @__PURE__ */ jsx(ArrowRight, { className: "w-4 h-4" })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsx(EventCarousel, { events: upcomingEvents }, 0)
      ] }),
      featuredEvents.length > 0 && /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-5 py-12", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold pl-3", children: translate("common.featured_events") }),
          /* @__PURE__ */ jsxs(
            Link,
            {
              href: route("events"),
              className: "btn btn-ghost gap-2",
              children: [
                translate("common.view_all_text"),
                " ",
                /* @__PURE__ */ jsx(ArrowRight, { className: "w-4 h-4" })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsx(EventCarousel, { events: featuredEvents }, 1)
      ] })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { children: [
      /* @__PURE__ */ jsx("div", { className: "bg-gray-200 py-16", children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold text-center mb-12", children: translate("common.customer_say_title") }),
        /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: homeTestimonials.map((testimonial, index) => /* @__PURE__ */ jsx(
          "div",
          {
            className: "card bg-base-100 shadow-xl",
            children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
              /* @__PURE__ */ jsx("div", { className: "flex items-center mb-4", children: [...Array(testimonial.rating)].map(
                (_, i) => /* @__PURE__ */ jsx(
                  Star,
                  {
                    className: "w-5 h-5 text-yellow-400 fill-current"
                  },
                  i
                )
              ) }),
              /* @__PURE__ */ jsxs("p", { className: "mb-4", children: [
                '"',
                testimonial.comment,
                '"'
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-600", children: [
                /* @__PURE__ */ jsx("p", { className: "font-semibold", children: testimonial.name }),
                /* @__PURE__ */ jsx("p", { children: testimonial.location })
              ] })
            ] })
          },
          index
        )) })
      ] }) }),
      /* @__PURE__ */ jsx("div", { className: "py-16", children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold text-center mb-12", children: translate("common.features_title") }),
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8", children: [
          /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body items-center text-center", children: [
            /* @__PURE__ */ jsx(Search$1, { className: "w-12 h-12 text-primary mb-4" }),
            /* @__PURE__ */ jsx("h3", { className: "card-title", children: translate("common.feature_1_title") }),
            /* @__PURE__ */ jsx("p", { children: translate(
              "common.feature_1_description"
            ) })
          ] }) }),
          /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body items-center text-center", children: [
            /* @__PURE__ */ jsx(Globe2, { className: "w-12 h-12 text-primary mb-4" }),
            /* @__PURE__ */ jsx("h3", { className: "card-title", children: translate("common.feature_2_title") }),
            /* @__PURE__ */ jsx("p", { children: translate(
              "common.feature_2_description"
            ) })
          ] }) }),
          /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body items-center text-center", children: [
            /* @__PURE__ */ jsx(CreditCard, { className: "w-12 h-12 text-primary mb-4" }),
            /* @__PURE__ */ jsx("h3", { className: "card-title", children: translate("common.feature_3_title") }),
            /* @__PURE__ */ jsx("p", { children: translate(
              "common.feature_3_description"
            ) })
          ] }) }),
          /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-xl", children: /* @__PURE__ */ jsxs("div", { className: "card-body items-center text-center", children: [
            /* @__PURE__ */ jsx(Ticket, { className: "w-12 h-12 text-primary mb-4" }),
            /* @__PURE__ */ jsx("h3", { className: "card-title", children: translate("common.feature_4_title") }),
            /* @__PURE__ */ jsx("p", { children: translate(
              "common.feature_4_description"
            ) })
          ] }) })
        ] })
      ] }) })
    ] })
  ] });
}
Home.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_16 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Home
}, Symbol.toStringTag, { value: "Module" }));
function LeagueCard({ league }) {
  return /* @__PURE__ */ jsxs("div", { className: "card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow", children: [
    /* @__PURE__ */ jsx("figure", { className: "relative h-48 bg-gray-200", children: league.image !== "" ? /* @__PURE__ */ jsx(
      "img",
      {
        src: league.image,
        alt: league.image_alt || league.name,
        className: "w-full h-full object-cover"
      }
    ) : /* @__PURE__ */ jsx("img", { src: "/img/ticketgol-logo.png", alt: league.name }) }),
    /* @__PURE__ */ jsx("div", { className: "card-body", children: /* @__PURE__ */ jsx(
      "div",
      {
        className: "tooltip tooltip-neutral w-fit text-left",
        "data-tip": league.name,
        tabIndex: 0,
        children: /* @__PURE__ */ jsx("h2", { className: "card-title line-clamp-1 max-w-xs", children: league.name })
      }
    ) })
  ] });
}
const fetchLeagues = createAsyncThunk(
  "leagues/fetchLeagues",
  async ({ url, filters, canAppendLeagues = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendLeagues, ...response.data };
    } else {
      throw new Error("Failed to fetch leagues");
    }
  }
);
const fetchFilterOptions$1 = createAsyncThunk(
  "leagues/fetchFilterOptions",
  async () => {
    const response = await axios$1.get(route("api.leagues.filters"));
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error("Failed to fetch filter options");
    }
  }
);
const initialState$8 = {
  leagues: [],
  loading: true,
  nextPageUrl: null,
  filterOptions: {},
  filterOptionsLoading: false,
  isFilterOptionsInitialized: false,
  filters: {
    seasons: [],
    countries: [],
    search: "",
    sort: ""
  }
};
const leaguesSlice = createSlice({
  name: "leagues",
  initialState: initialState$8,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$8.filters;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    resetNextPageUrl: (state) => {
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchLeagues.pending, (state) => {
      state.loading = true;
    }).addCase(fetchLeagues.fulfilled, (state, action) => {
      const { leagues, meta } = action.payload;
      if (action.payload.canAppendLeagues) {
        const existingLeaguesMap = new Map(
          state.leagues.map((league) => [league.id, league])
        );
        const newLeagues = leagues.filter(
          (league) => !existingLeaguesMap.has(league.id)
        );
        state.leagues = [...state.leagues, ...newLeagues];
      } else {
        state.leagues = leagues;
      }
      state.nextPageUrl = meta.next_page_url;
      state.loading = false;
    }).addCase(fetchLeagues.rejected, (state) => {
      state.leagues = [];
      state.loading = false;
    });
    builder.addCase(fetchFilterOptions$1.pending, (state) => {
      state.filterOptionsLoading = true;
    }).addCase(fetchFilterOptions$1.fulfilled, (state, action) => {
      state.filterOptions = action.payload;
      state.filterOptionsLoading = false;
      state.isFilterOptionsInitialized = true;
    }).addCase(fetchFilterOptions$1.rejected, (state) => {
      state.filterOptions = {};
      state.filterOptionsLoading = false;
    });
  }
});
const { resetFilters: resetFilters$3, setFilter: setFilter$2, resetNextPageUrl: resetNextPageUrl$1 } = leaguesSlice.actions;
const leaguesReducer = leaguesSlice.reducer;
function useLeagues() {
  const {
    leagues,
    loading,
    filters,
    filterOptions,
    nextPageUrl,
    isFilterOptionsInitialized
  } = useSelector((state) => state.leagues);
  const dispatch = useDispatch();
  const updateFilter = (key, value) => {
    dispatch(setFilter$2({ key, value }));
  };
  const clearFilters = useCallback(() => {
    dispatch(resetFilters$3());
  }, []);
  const fetchOptions = () => {
    if (isFilterOptionsInitialized) {
      return;
    }
    dispatch(fetchFilterOptions$1());
  };
  const fetchLeaguesInitially = () => {
    if (isFilterOptionsInitialized > 0) {
      return;
    }
    dispatch(
      fetchLeagues({
        url: route("api.leagues.index"),
        filters
      })
    );
  };
  const refreshLeagues = () => {
    dispatch(
      fetchLeagues({
        url: route("api.leagues.index"),
        filters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl$1());
  };
  const loadMoreLeagues = () => {
    if (!nextPageUrl) {
      return;
    }
    return dispatch(
      fetchLeagues({
        url: nextPageUrl,
        filters,
        canAppendLeagues: true
      })
    );
  };
  return {
    leagues,
    loading,
    filterOptions,
    filters,
    updateFilter,
    clearFilters,
    fetchOptions,
    fetchLeaguesInitially,
    refreshLeagues,
    nextPageUrl,
    loadMoreLeagues,
    clearNextPageUrl
  };
}
function LeagueSidebar() {
  const { translate } = useTranslations();
  const {
    filters,
    clearFilters,
    updateFilter,
    filterOptions,
    refreshLeagues
  } = useLeagues();
  const debouncedFilters = useDebounce(filters, 800);
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    refreshLeagues();
  }, [debouncedFilters]);
  return /* @__PURE__ */ jsxs("aside", { className: "md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3", children: [
    /* @__PURE__ */ jsx("h2", { className: "font-semibold mt-8 mb-4", children: translate("leagues.filters_title", "Filters") }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "text",
          placeholder: translate(
            "leagues.search_placeholder",
            "Search leagues..."
          ),
          className: "input input-bordered w-full",
          value: (filters == null ? void 0 : filters.search) || "",
          onChange: (e) => updateFilter("search", e.target.value)
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: clearFilters,
          className: "text-gray-500 hover:text-gray-800 transition-colors",
          title: translate("leagues.reset_filters", "Reset Filters"),
          children: /* @__PURE__ */ jsx(XCircle, { size: 24 })
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate("leagues.countries_title", "Countries"),
        placeholder: translate(
          "leagues.countries_placeholder",
          "Select countries"
        ),
        options: filterOptions["countries"],
        selectedOption: filters["countries"],
        onChange: (value) => {
          updateFilter("countries", value);
        },
        isMulti: true
      },
      "countries"
    ),
    /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate("leagues.seasons_title", "Seasons"),
        placeholder: translate(
          "leagues.seasons_placeholder",
          "Select seasons"
        ),
        options: filterOptions["seasons"],
        selectedOption: filters["seasons"],
        onChange: (value) => {
          updateFilter("seasons", value);
        },
        isMulti: true
      },
      "seasons"
    )
  ] });
}
function Leagues() {
  const { translate } = useTranslations();
  const {
    leagues,
    filters,
    updateFilter,
    fetchOptions,
    loading,
    loadMoreLeagues,
    clearNextPageUrl,
    nextPageUrl,
    fetchLeaguesInitially
  } = useLeagues();
  useEffect(() => {
    clearNextPageUrl();
    fetchOptions();
    const timer = setTimeout(() => {
      fetchLeaguesInitially();
    }, 10);
    return () => clearTimeout(timer);
  }, []);
  const [observerRef] = useInfiniteScroll({
    loading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreLeagues,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("leagues.head_title", "Leagues") }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8", children: [
      /* @__PURE__ */ jsx(LeagueSidebar, {}),
      /* @__PURE__ */ jsxs("main", { className: "md:w-3/4 w-full", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-8", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-2xl md:text-4xl font-bold", children: translate("leagues.page_title", "All Leagues") }),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              wrapperClass: "w-1/2 md:w-1/3",
              options: translate("leagues.sort_options"),
              value: filters.sort ? translate("leagues.sort_options").find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (selected) => updateFilter("sort", selected.value),
              placeholder: translate(
                "leagues.sort_by_placeholder"
              )
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "h-[800px] overflow-y-auto my-5", children: !loading && leagues.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate(
            "leagues.no_leagues",
            "Sorry, no leagues match your filters."
          ) }),
          /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("leagues.no_leagues_details") })
        ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: leagues.map((league, index) => /* @__PURE__ */ jsx(
            Link,
            {
              href: route(
                "detail.show",
                league.slug
              ),
              className: "hover:scale-[1.02] transition-transform duration-150",
              children: /* @__PURE__ */ jsx(LeagueCard, { league })
            },
            `${league.id}-${index}`
          )) }),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          ),
          loading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
        ] }) })
      ] })
    ] })
  ] });
}
Leagues.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_17 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Leagues
}, Symbol.toStringTag, { value: "Module" }));
const fetchLeagueDetail = createAsyncThunk(
  "leagues/fetchLeagueDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.league;
    } else {
      throw new Error("Failed to fetch league detail");
    }
  }
);
const initialState$7 = {
  league: null,
  leagueLoading: true
};
const leagueDetailSlice = createSlice({
  name: "league",
  initialState: initialState$7,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchLeagueDetail.pending, (state) => {
      state.leagueLoading = true;
    }).addCase(fetchLeagueDetail.fulfilled, (state, action) => {
      state.league = action.payload;
      state.leagueLoading = false;
    }).addCase(fetchLeagueDetail.rejected, (state) => {
      state.league = null;
      state.leagueLoading = false;
    });
  }
});
const leagueDetailReducer = leagueDetailSlice.reducer;
function useLeagueDetail() {
  const { league, leagueLoading } = useSelector((state) => state.league);
  const dispatch = useDispatch();
  const getLeagueDetail = (slug) => {
    dispatch(
      fetchLeagueDetail({
        url: route("api.leagues.show", { slug })
      })
    );
  };
  return {
    league,
    leagueLoading,
    getLeagueDetail
  };
}
function LeagueEventList() {
  const { translate } = useTranslations();
  const { league } = useLeagueDetail();
  const {
    events,
    filters,
    updateFilter,
    eventLoading,
    loadMoreEvents,
    clearNextPageUrl,
    clearFilters,
    nextPageUrl,
    fetchEventsInitially
  } = useEvents();
  useEffect(() => {
    clearFilters();
    clearNextPageUrl();
    updateFilter("leagues", [league.id]);
  }, [league.id]);
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchEventsInitially();
    }, 50);
    return () => clearTimeout(timer);
  }, [filters]);
  const [observerRef] = useInfiniteScroll({
    loading: eventLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEvents,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col md:flex-row md:justify-between md:items-center gap-2 mb-2 mt-8", children: [
      /* @__PURE__ */ jsxs("h1", { className: "text-base lg:text-2xl md:text-lg font-bold", children: [
        translate("leagues.events_in_text"),
        " ",
        league.name
      ] }),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          wrapperClass: "w-full md:w-1/4",
          options: translate("leagues.event_sort_options"),
          value: filters.sort ? translate("leagues.event_sort_options").find(
            (option) => option.value === filters.sort
          ) : null,
          onChange: (selected) => updateFilter("sort", selected.value),
          placeholder: translate("leagues.sort_by_placeholder")
        }
      )
    ] }),
    !eventLoading && events.length === 0 ? /* @__PURE__ */ jsx("div", { className: "flex flex-col items-center justify-center px-5 h-96 bg-base-100 mt-5 rounded-xl shadow-sm", children: /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("leagues.no_events") }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("div", { className: "container mx-auto py-5 grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4", children: events.map((event, index) => /* @__PURE__ */ jsx(
        "div",
        {
          className: "hover:scale-[1.02] cursor-pointer transition-transform duration-150",
          children: /* @__PURE__ */ jsx(
            EventCard,
            {
              event,
              translationFrom: translate("common.from"),
              translationBuyTickets: translate(
                "common.buy_tickets"
              )
            }
          )
        },
        event.id
      )) }),
      nextPageUrl && /* @__PURE__ */ jsx("div", { ref: observerRef, className: "h-10" }),
      eventLoading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-32", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] })
  ] });
}
function Show$1() {
  const { slug } = usePage().props;
  const { league, getLeagueDetail, leagueLoading } = useLeagueDetail();
  useEffect(() => {
    getLeagueDetail(slug);
  }, []);
  if (leagueLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: league.name }),
      /* @__PURE__ */ jsx("meta", { name: "title", content: league.meta_title }),
      /* @__PURE__ */ jsx("meta", { name: "keywords", content: league.meta_keywords }),
      /* @__PURE__ */ jsx("meta", { name: "description", content: league.meta_description })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-10 py-5", children: [
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        league.image !== "" ? /* @__PURE__ */ jsx(
          "img",
          {
            src: league.image,
            alt: league.image_alt || league.name,
            className: "w-full h-96 object-cover rounded-xl"
          }
        ) : /* @__PURE__ */ jsx("div", { className: "bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500", children: /* @__PURE__ */ jsx(
          "img",
          {
            src: "/img/ticketgol-logo.png",
            alt: league.name,
            className: "object-cover w-1/3"
          }
        ) }),
        /* @__PURE__ */ jsxs("div", { className: "absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-xl sm:text-3xl font-bold mb-2", children: league.name }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-warning font-medium", children: [
            /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsx("span", { className: "text-base sm:text-lg", children: league.country.name })
          ] }),
          /* @__PURE__ */ jsx("p", { className: "text-sm sm:text-base mt-2 max-w-xl", children: league.description })
        ] })
      ] }),
      /* @__PURE__ */ jsx(LeagueEventList, {})
    ] })
  ] });
}
Show$1.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_18 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Show$1
}, Symbol.toStringTag, { value: "Module" }));
function MyAccountSidebar({ activeMenu }) {
  const { auth } = usePage().props;
  const { translate } = useTranslations();
  const accountMenuItems = [
    {
      icon: /* @__PURE__ */ jsx(Wallet, { className: "w-5 h-5" }),
      title: "Dashboard",
      route: route("my-account.index"),
      key: "dashboard"
    },
    {
      icon: /* @__PURE__ */ jsx(User, { className: "w-5 h-5" }),
      title: "My Profile",
      route: route("profile.edit"),
      key: "profile"
    },
    {
      icon: /* @__PURE__ */ jsx(ShoppingBag, { className: "w-5 h-5" }),
      title: "My Orders",
      route: route("my-account.orders"),
      key: "orders"
    },
    {
      icon: /* @__PURE__ */ jsx(Ticket, { className: "w-5 h-5" }),
      title: "My Tickets",
      route: route("my-account.tickets"),
      key: "tickets"
    },
    {
      icon: /* @__PURE__ */ jsx(MessageSquare, { className: "w-5 h-5" }),
      title: "My Support Requests",
      route: "#support",
      key: "support"
    }
  ];
  return /* @__PURE__ */ jsx("aside", { className: "overflow-y-auto px-6 w-full max-h-screen bg-white rounded-lg shadow-md md:w-1/4 md:sticky md:top-3", children: /* @__PURE__ */ jsxs("div", { className: "py-6", children: [
    /* @__PURE__ */ jsx("h2", { className: "mb-6 text-xl font-semibold", children: translate("common.menu.my_account") }),
    /* @__PURE__ */ jsx("div", { className: "flex flex-col space-y-2", children: accountMenuItems.map((item) => /* @__PURE__ */ jsxs(
      Link,
      {
        href: item.route,
        className: `flex items-center p-3 rounded-lg transition-colors ${activeMenu === item.key ? "bg-primary text-white" : "hover:bg-gray-50"}`,
        children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: `mr-3 ${activeMenu === item.key ? "text-white" : "text-primary"}`,
              children: item.icon
            }
          ),
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: item.title })
        ]
      },
      item.key
    )) })
  ] }) });
}
function MyAccountContent({ activeMenu, children }) {
  if (!children) {
    switch (activeMenu) {
      case "profile":
        router.visit(route("profile.edit"));
        return null;
      case "orders":
        router.visit(route("my-account.orders"));
        return null;
      // Add other cases as needed
      default:
        return /* @__PURE__ */ jsx("div", { className: "bg-white p-6 rounded-lg shadow-md", children: /* @__PURE__ */ jsx("p", { className: "text-gray-500", children: "Select an option from the sidebar" }) });
    }
  }
  return /* @__PURE__ */ jsx("div", { className: "md:w-3/4 w-full", children: /* @__PURE__ */ jsx("div", { className: "bg-white p-6 rounded-lg shadow-md", children }) });
}
function MyAccountLayout(props) {
  const { auth, activeMenu, children } = props;
  useTranslations();
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx("div", { className: "py-3", children: /* @__PURE__ */ jsxs("div", { className: "container px-4 mx-auto", children: [
    /* @__PURE__ */ jsxs("h1", { className: "mb-8 text-2xl font-bold", children: [
      "Welcome Back, ",
      auth.user.name
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-6 md:flex-row", children: [
      /* @__PURE__ */ jsx(MyAccountSidebar, { activeMenu }),
      /* @__PURE__ */ jsx(MyAccountContent, { activeMenu, children })
    ] })
  ] }) }) });
}
MyAccountLayout.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
function Dashboard(props) {
  const { auth, activeMenu, children } = props;
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      Head,
      {
        title: `${translate("common.menu.my_account.dashboard", "Dashboard")} - ${auth.user.name}`
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "py-3", children: /* @__PURE__ */ jsxs("div", { className: "text-gray-900", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-4 text-xl font-semibold", children: translate("my_account.dashboard.title", "Dashboard") }),
      /* @__PURE__ */ jsx("p", { className: "mb-4", children: translate(
        "my_account.dashboard.welcome_message",
        "Welcome to your account dashboard. Use the sidebar to navigate to different sections of your account."
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-4 mt-6 md:grid-cols-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "p-4 bg-gray-50 rounded-lg border", children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-medium", children: translate(
            "my_account.dashboard.recent_orders",
            "Recent Orders"
          ) }),
          /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-600", children: translate(
            "my_account.dashboard.no_recent_orders",
            "You don't have any recent orders."
          ) })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-4 bg-gray-50 rounded-lg border", children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-medium", children: translate(
            "my_account.dashboard.upcoming_events",
            "Upcoming Events"
          ) }),
          /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-600", children: translate(
            "my_account.dashboard.no_upcoming_events",
            "You don't have any upcoming events."
          ) })
        ] })
      ] })
    ] }) })
  ] });
}
Dashboard.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "dashboard"
    }
  ) });
};
const __vite_glob_0_19 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Dashboard
}, Symbol.toStringTag, { value: "Module" }));
function useOrderDetail(orderId) {
  const [order, setOrder] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const fetchOrderDetail = async () => {
    var _a, _b;
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios$1.get(route("api.orders.show", orderId));
      if (response.data.success === true) {
        setOrder(response.data.order);
      } else {
        toast.error("Failed to fetch order details");
        setError("Failed to fetch order details");
      }
    } catch (error2) {
      toast.error("Failed to fetch order details");
      setError(
        ((_b = (_a = error2.response) == null ? void 0 : _a.data) == null ? void 0 : _b.message) || "An error occurred while fetching order details"
      );
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    if (orderId) {
      fetchOrderDetail();
    }
  }, [orderId]);
  return {
    order,
    isLoading,
    error,
    fetchOrderDetail
  };
}
function InfoCard({ title, children, className = "" }) {
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs(
    "div",
    {
      className: `bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden ${className}`,
      children: [
        /* @__PURE__ */ jsx("div", { className: "bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-gray-900", children: title }) }),
        /* @__PURE__ */ jsx("div", { className: "p-6", children })
      ]
    }
  ) });
}
const formatDate = (dateString, format = "DD/MM/YYYY") => {
  if (!dateString) return "";
  return dayjs(dateString).format(format);
};
const formatTime = (timeString) => {
  if (!timeString) return null;
  try {
    if (timeString.includes("T") || timeString.includes("-")) {
      return new Date(timeString).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit"
      });
    }
    return (/* @__PURE__ */ new Date(`1970-01-01T${timeString}`)).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit"
    });
  } catch {
    return timeString;
  }
};
const formatCurrency = (amount, currency = "EUR") => {
  if (!amount) return "N/A";
  return new Intl.NumberFormat("en-EU", {
    style: "currency",
    currency
  }).format(amount);
};
function OrderSummary({ order }) {
  var _a, _b, _c, _d;
  const { translate } = useTranslations();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const getStatusBadgeClass = (color) => {
    switch (color) {
      case "success":
        return "bg-green-100 text-green-800 border-green-200";
      case "warning":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "danger":
        return "bg-red-100 text-red-800 border-red-200";
      case "info":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx(InfoCard, { title: translate("order.summary_title", "Order Summary"), children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-4 justify-between items-start lg:flex-row lg:items-center", children: [
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex gap-3 items-center", children: [
        /* @__PURE__ */ jsxs("h2", { className: "text-2xl font-bold text-gray-900", children: [
          "#",
          order.order_no
        ] }),
        /* @__PURE__ */ jsx(
          "span",
          {
            className: `px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(
              order.status.color
            )}`,
            children: order.status.label
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap gap-4 text-sm text-gray-600", children: [
        /* @__PURE__ */ jsxs("span", { children: [
          /* @__PURE__ */ jsxs("strong", { children: [
            translate("order.created_at", "Created"),
            ":"
          ] }),
          " ",
          formatDate(order.created_at)
        ] }),
        order.purchase_date && /* @__PURE__ */ jsxs("span", { children: [
          /* @__PURE__ */ jsxs("strong", { children: [
            translate(
              "order.purchase_date",
              "Purchased"
            ),
            ":"
          ] }),
          " ",
          formatDate(order.purchase_date)
        ] })
      ] }),
      (((_a = order.ticket) == null ? void 0 : _a.ticket_no) || ((_b = order.ticket) == null ? void 0 : _b.type)) && /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-4 p-3 mt-3 bg-blue-50 rounded-lg border border-blue-200", children: [
        ((_c = order.ticket) == null ? void 0 : _c.ticket_no) && /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center", children: [
          /* @__PURE__ */ jsxs("span", { className: "text-sm font-medium text-blue-700", children: [
            translate(
              "order.ticket_number",
              "Ticket No"
            ),
            ":"
          ] }),
          /* @__PURE__ */ jsxs("span", { className: "text-lg font-bold text-blue-900", children: [
            "#",
            order.ticket.ticket_no
          ] })
        ] }),
        ((_d = order.ticket) == null ? void 0 : _d.ticket_type) && /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center", children: [
          /* @__PURE__ */ jsxs("span", { className: "text-sm font-medium text-blue-700", children: [
            translate(
              "order.ticket_type",
              "Ticket Type"
            ),
            ":"
          ] }),
          /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-blue-900", children: order.ticket.ticket_type })
        ] })
      ] }),
      order.description && /* @__PURE__ */ jsx("p", { className: "mt-2 text-gray-700", children: order.description })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "text-right", children: [
      /* @__PURE__ */ jsx("p", { className: "mb-1 text-sm text-gray-600", children: translate("order.total", "Total Amount") }),
      /* @__PURE__ */ jsx("p", { className: "text-3xl font-bold text-green-600", children: formatCurrency(order.total_price) }),
      /* @__PURE__ */ jsxs("p", { className: "mb-4 text-sm text-gray-600", children: [
        translate("order.quantity", "Quantity"),
        ":",
        " ",
        order.quantity,
        " ",
        translate(
          "order.details.common.tickets",
          "tickets"
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-2", children: [
        order.status.value === "confirmed" && /* @__PURE__ */ jsxs("button", { className: "inline-flex gap-2 justify-center items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg transition-colors duration-200 hover:bg-green-700", children: [
          /* @__PURE__ */ jsx(
            "svg",
            {
              className: "w-4 h-4",
              fill: "currentColor",
              viewBox: "0 0 20 20",
              children: /* @__PURE__ */ jsx(
                "path",
                {
                  fillRule: "evenodd",
                  d: "M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",
                  clipRule: "evenodd"
                }
              )
            }
          ),
          translate(
            "order.download_ticket",
            "Download Ticket"
          )
        ] }),
        (order.status.value === "pending" || order.status.value === "confirmed") && /* @__PURE__ */ jsxs(
          "button",
          {
            onClick: () => setShowCancelModal(true),
            className: "inline-flex gap-2 justify-center items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-lg border border-red-200 transition-colors duration-200 hover:bg-red-100 hover:border-red-300",
            children: [
              /* @__PURE__ */ jsx(
                "svg",
                {
                  className: "w-4 h-4",
                  fill: "currentColor",
                  viewBox: "0 0 20 20",
                  children: /* @__PURE__ */ jsx(
                    "path",
                    {
                      fillRule: "evenodd",
                      d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",
                      clipRule: "evenodd"
                    }
                  )
                }
              ),
              translate("order.cancel", "Cancel Order")
            ]
          }
        )
      ] })
    ] })
  ] }) }) });
}
function InfoItem({ label, value, className = "", icon, highlight: highlight2 = false }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs("div", { className: `${className}`, children: [
    /* @__PURE__ */ jsxs("p", { className: "text-sm font-medium text-gray-600 mb-1 flex items-center gap-2", children: [
      icon && /* @__PURE__ */ jsx(
        "span",
        {
          className: `${highlight2 ? "text-primary-600" : "text-gray-500"}`,
          children: icon
        }
      ),
      label,
      ":"
    ] }),
    /* @__PURE__ */ jsx(
      "p",
      {
        className: `font-medium ${highlight2 ? "text-primary-600" : "text-gray-900"}`,
        children: value || translate("common.not_available", "N/A")
      }
    )
  ] });
}
function EventInfo({ event }) {
  var _a, _b;
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(InfoCard, { title: translate("events.info", "Event Information"), children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-6", children: [
    /* @__PURE__ */ jsx("div", { className: "lg:col-span-1", children: /* @__PURE__ */ jsxs("div", { className: "relative aspect-w-16 aspect-h-9 rounded-xl overflow-hidden bg-gray-100 shadow-lg", children: [
      /* @__PURE__ */ jsx(
        "img",
        {
          src: event.image || "/img/ticketgol-logo.png",
          alt: event.image_alt || event.name,
          className: "w-full h-48 object-cover rounded-xl"
        }
      ),
      /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" }),
      /* @__PURE__ */ jsx("div", { className: "absolute bottom-4 left-4 text-white", children: /* @__PURE__ */ jsx("p", { className: "text-sm font-medium", children: (_a = event.category) == null ? void 0 : _a.label }) })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "lg:col-span-2", children: [
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.name", "Event Name"),
            value: event.name,
            className: "md:col-span-2",
            highlight: true,
            icon: /* @__PURE__ */ jsx(Target, { size: 18 })
          }
        ),
        event.description && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "events.description",
              "Description"
            ),
            value: event.description,
            className: "md:col-span-2",
            icon: /* @__PURE__ */ jsx(FileText, { size: 18 })
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.category", "Category"),
            value: (_b = event.category) == null ? void 0 : _b.label,
            icon: /* @__PURE__ */ jsx(Tag, { size: 18 })
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.date", "Date"),
            value: event.date ? formatDate(event.date) : null,
            icon: /* @__PURE__ */ jsx(CalendarDays, { size: 18 }),
            highlight: true
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.time", "Time"),
            value: event.time,
            icon: /* @__PURE__ */ jsx(Clock, { size: 18 }),
            highlight: true
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.timezone", "Timezone"),
            value: event.timezone,
            icon: /* @__PURE__ */ jsx(Globe, { size: 18 })
          }
        ),
        event.country && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.country", "Country"),
            value: event.country.name,
            icon: /* @__PURE__ */ jsx(Flag, { size: 18 })
          }
        ),
        event.league && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("events.league", "League"),
            value: event.league.name,
            icon: /* @__PURE__ */ jsx(Trophy, { size: 18 })
          }
        )
      ] }),
      (event.home_club || event.guest_club) && /* @__PURE__ */ jsxs("div", { className: "mt-6 pt-6 border-t border-gray-200", children: [
        /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 mb-4 flex items-center gap-2", children: [
          /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(Dribbble, { size: 18 }) }),
          translate("events.order.clubs", "Teams")
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
          event.home_club && /* @__PURE__ */ jsx("div", { className: "bg-green-50 p-4 rounded-lg border border-green-200", children: /* @__PURE__ */ jsx(
            InfoItem,
            {
              label: translate(
                "events.home_club",
                "Home Club"
              ),
              value: event.home_club.name,
              icon: /* @__PURE__ */ jsx(House, { size: 18 })
            }
          ) }),
          event.guest_club && /* @__PURE__ */ jsx("div", { className: "bg-blue-50 p-4 rounded-lg border border-blue-200", children: /* @__PURE__ */ jsx(
            InfoItem,
            {
              label: translate(
                "events.guest_club",
                "Guest Club"
              ),
              value: event.guest_club.name,
              icon: /* @__PURE__ */ jsx(Plane, { size: 18 })
            }
          ) })
        ] })
      ] })
    ] })
  ] }) });
}
function StadiumInfo({ stadium }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(InfoCard, { title: translate("stadiums.info", "Stadium Information"), children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: [
    /* @__PURE__ */ jsx(
      InfoItem,
      {
        label: translate("stadiums.name", "Stadium Name"),
        value: stadium.name,
        highlight: true,
        icon: /* @__PURE__ */ jsx(Building, { size: 18 })
      }
    ),
    /* @__PURE__ */ jsx(
      InfoItem,
      {
        label: translate("stadiums.address", "Address"),
        value: /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsxs("p", { children: [
            stadium.address_line_1,
            stadium.address_line_2 ? "," : ""
          ] }),
          /* @__PURE__ */ jsx("p", { children: stadium.address_line_2 })
        ] }),
        icon: /* @__PURE__ */ jsx(MapPin, { size: 18 })
      }
    ),
    /* @__PURE__ */ jsx(
      InfoItem,
      {
        label: translate("stadiums.postcode", "Postcode"),
        value: stadium.postcode,
        icon: /* @__PURE__ */ jsx(MailOpen, { size: 18 })
      }
    ),
    /* @__PURE__ */ jsx(
      InfoItem,
      {
        label: translate("stadiums.country", "Country"),
        value: stadium.country,
        icon: /* @__PURE__ */ jsx(Globe, { size: 18 })
      }
    )
  ] }) });
}
function TicketInfo({ ticket }) {
  var _a;
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs(InfoCard, { title: translate("ticket.info", "Ticket Information"), children: [
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3", children: [
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.ticket_number", "Ticket Number"),
          value: `#${ticket.ticket_no}`,
          highlight: true,
          icon: /* @__PURE__ */ jsx(Ticket, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.type", "Ticket Type"),
          value: (_a = ticket.ticket_type) == null ? void 0 : _a.label,
          icon: /* @__PURE__ */ jsx(Tag, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.price", "Price"),
          value: ticket.price ? `${formatCurrency(ticket.price)}` : null,
          highlight: true,
          icon: /* @__PURE__ */ jsx(Banknote, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.face_value_price", "Face Value"),
          value: ticket.face_value_price ? `${formatCurrency(ticket.face_value_price)}` : null,
          icon: /* @__PURE__ */ jsx(DollarSign, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.quantity", "Quantity"),
          value: ticket.quantity,
          icon: /* @__PURE__ */ jsx(Hash, { size: 18 })
        }
      ),
      ticket.sector && /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.sector", "Sector"),
          value: ticket.sector.name,
          highlight: true,
          icon: /* @__PURE__ */ jsx(Target, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.rows", "Rows"),
          value: ticket.ticket_rows,
          highlight: true,
          icon: /* @__PURE__ */ jsx(MapPin, { size: 18 })
        }
      ),
      /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.seats", "Seats"),
          value: ticket.ticket_seats,
          highlight: true,
          icon: /* @__PURE__ */ jsx(Armchair, { size: 18 })
        }
      ),
      ticket.description && /* @__PURE__ */ jsx(
        InfoItem,
        {
          label: translate("ticket.description", "Description"),
          value: ticket.description,
          className: "lg:col-span-3",
          icon: /* @__PURE__ */ jsx(FileText, { size: 18 })
        }
      )
    ] }),
    ticket.seller && /* @__PURE__ */ jsxs("div", { className: "pt-6 mt-6 border-t border-gray-200", children: [
      /* @__PURE__ */ jsxs("h4", { className: "flex gap-2 items-center mb-4 font-semibold text-gray-900", children: [
        /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(User, { size: 18 }) }),
        translate(
          "order.details.ticket.seller_info",
          "Seller Information"
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "p-4 bg-yellow-50 rounded-lg border border-yellow-200", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_name",
              "Seller Name"
            ),
            value: ticket.seller.name,
            icon: /* @__PURE__ */ jsx(User, { size: 18 })
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_email",
              "Seller Email"
            ),
            value: ticket.seller.email,
            icon: /* @__PURE__ */ jsx(Mail, { size: 18 })
          }
        ),
        ticket.seller.phone && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_phone",
              "Phone"
            ),
            value: ticket.seller.phone,
            icon: /* @__PURE__ */ jsx(Phone, { size: 18 })
          }
        ),
        ticket.seller.company && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_company",
              "Company"
            ),
            value: ticket.seller.company,
            icon: /* @__PURE__ */ jsx(Building, { size: 18 })
          }
        ),
        (ticket.seller.address || ticket.seller.city || ticket.seller.country) && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_address",
              "Address"
            ),
            value: [
              ticket.seller.address && `${ticket.seller.address}, `,
              ticket.seller.city && `${ticket.seller.city}, `,
              ticket.seller.country
            ].filter(Boolean).join("").replace(/,\s*$/, ""),
            icon: /* @__PURE__ */ jsx(MapPin, { size: 18 })
          }
        ),
        ticket.seller.description && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "ticket.seller_description",
              "Description"
            ),
            value: ticket.seller.description,
            icon: /* @__PURE__ */ jsx(FileText, { size: 18 })
          }
        )
      ] }) })
    ] })
  ] });
}
function BuyerInfo({ buyer }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(
    InfoCard,
    {
      title: translate(
        "order.details.buyer.information",
        "Buyer Information"
      ),
      children: /* @__PURE__ */ jsx("div", { className: "p-6 bg-blue-50 rounded-lg border border-blue-200", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3", children: [
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("order.details.buyer.name", "Name"),
            value: buyer.name,
            icon: /* @__PURE__ */ jsx(User, { size: 18 })
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("order.details.buyer.email", "Email"),
            value: buyer.email,
            icon: /* @__PURE__ */ jsx(Mail, { size: 18 })
          }
        ),
        /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate("order.details.buyer.phone", "Phone"),
            value: buyer.phone,
            icon: /* @__PURE__ */ jsx(Phone, { size: 18 })
          }
        ),
        buyer.address && /* @__PURE__ */ jsx(
          InfoItem,
          {
            label: translate(
              "order.details.buyer.address",
              "Address"
            ),
            value: buyer.address,
            className: "lg:col-span-3",
            icon: /* @__PURE__ */ jsx(Home$1, { size: 18 })
          }
        )
      ] }) })
    }
  );
}
function RestrictionsInfo({ restrictions }) {
  const { translate } = useTranslations();
  const getRestrictionBadgeClass = (type) => {
    switch (type) {
      case "age":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "gender":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "identification":
        return "bg-indigo-100 text-indigo-800 border-indigo-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };
  return /* @__PURE__ */ jsx(
    InfoCard,
    {
      title: translate(
        "order.restrictions_title",
        "Restrictions & Requirements"
      ),
      icon: /* @__PURE__ */ jsx(
        "svg",
        {
          className: "w-6 h-6",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            }
          )
        }
      ),
      children: /* @__PURE__ */ jsx("div", { className: "space-y-4", children: restrictions.map((restriction, index) => /* @__PURE__ */ jsxs(
        "div",
        {
          className: "flex gap-4 items-start p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200 shadow-sm",
          children: [
            /* @__PURE__ */ jsx("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsx(
              "span",
              {
                className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getRestrictionBadgeClass(
                  restriction.type
                )}`,
                children: restriction.type_label || restriction.type
              }
            ) }),
            /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsx("h5", { className: "mb-1 font-semibold text-gray-900", children: restriction.name }) }),
            /* @__PURE__ */ jsx("div", { className: "text-orange-500", children: /* @__PURE__ */ jsx(AlertTriangle, { size: 20 }) })
          ]
        },
        restriction.id || index
      )) })
    }
  );
}
function TransactionInfo({ transactions }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(
    InfoCard,
    {
      title: translate(
        "order.transactions.title",
        "Payment & Transaction Details"
      ),
      children: /* @__PURE__ */ jsx("div", { className: "space-y-6", children: transactions.map((transaction, index) => /* @__PURE__ */ jsxs(
        "div",
        {
          className: "p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-gray-200 shadow-sm",
          children: [
            /* @__PURE__ */ jsx("div", { className: "flex justify-between items-center mb-4", children: /* @__PURE__ */ jsxs("h4", { className: "flex gap-2 items-center font-semibold text-gray-900", children: [
              /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(CreditCard, { size: 18 }) }),
              translate(
                "order.transaction.title",
                "Transaction"
              ),
              " ",
              "#",
              index + 1
            ] }) }),
            /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3", children: [
              /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: translate(
                    "order.transaction.amount",
                    "Amount"
                  ),
                  value: `${formatCurrency(transaction.total_amount, transaction.currency_code)}`,
                  highlight: true,
                  icon: /* @__PURE__ */ jsx(Banknote, { size: 18 })
                }
              ),
              /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: `${translate("order.transaction.currency_code", "Currency code")}`,
                  value: transaction.currency_code
                }
              ),
              /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: translate(
                    "order.transaction.payment_method",
                    "Payment Method"
                  ),
                  value: transaction.payment_method_type,
                  icon: /* @__PURE__ */ jsx(CreditCard, { size: 18 })
                }
              ),
              /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: translate(
                    "order.transaction.paid_at",
                    "Paid At"
                  ),
                  value: transaction.paid_at ? formatDate(transaction.paid_at) : null,
                  icon: /* @__PURE__ */ jsx(CheckCircle, { size: 18 })
                }
              ),
              transaction.card_brand && /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: translate(
                    "order.transaction.card_details",
                    "Card Details"
                  ),
                  value: `${transaction.card_brand} **** ${transaction.card_last_four}`,
                  icon: /* @__PURE__ */ jsx(CreditCard, { size: 18 })
                }
              ),
              transaction.refunded_at && /* @__PURE__ */ jsx(
                InfoItem,
                {
                  label: translate(
                    "order.transaction.refunded_at",
                    "Refunded At"
                  ),
                  value: formatDate(transaction.refunded_at),
                  className: "lg:col-span-3",
                  icon: /* @__PURE__ */ jsx(RefreshCw, { size: 18 })
                }
              )
            ] })
          ]
        },
        transaction.id || index
      )) })
    }
  );
}
function AttendeesInfo({ attendees }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(
    InfoCard,
    {
      title: translate("order.attendees.title", "Attendees Information"),
      icon: /* @__PURE__ */ jsx(
        "svg",
        {
          className: "w-6 h-6",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            }
          )
        }
      ),
      children: /* @__PURE__ */ jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxs("table", { className: "overflow-hidden min-w-full rounded-lg divide-y divide-gray-200", children: [
        /* @__PURE__ */ jsx("thead", { className: "text-black", children: /* @__PURE__ */ jsxs("tr", { children: [
          /* @__PURE__ */ jsx("th", { className: "px-6 py-4 text-xs font-medium tracking-wider text-left uppercase", children: translate("order.attendees.name", "Name") }),
          /* @__PURE__ */ jsx("th", { className: "px-6 py-4 text-xs font-medium tracking-wider text-left uppercase", children: translate("order.attendees.email", "Email") }),
          /* @__PURE__ */ jsx("th", { className: "px-6 py-4 text-xs font-medium tracking-wider text-left uppercase", children: translate("order.attendees.gender", "Gender") }),
          /* @__PURE__ */ jsx("th", { className: "px-6 py-4 text-xs font-medium tracking-wider text-left uppercase", children: translate(
            "order.attendees.dob",
            "Date of Birth"
          ) })
        ] }) }),
        /* @__PURE__ */ jsx("tbody", { className: "bg-white divide-y divide-gray-200", children: attendees.map((attendee, index) => {
          var _a, _b;
          return /* @__PURE__ */ jsxs(
            "tr",
            {
              className: "transition-colors duration-200 hover:bg-blue-50",
              children: [
                /* @__PURE__ */ jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center", children: [
                  /* @__PURE__ */ jsx("div", { className: "flex justify-center items-center w-8 h-8 bg-blue-100 rounded-full", children: /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-blue-600", children: (_b = (_a = attendee.name) == null ? void 0 : _a.charAt(0)) == null ? void 0 : _b.toUpperCase() }) }),
                  /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-gray-900", children: attendee.name })
                ] }) }),
                /* @__PURE__ */ jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: attendee.email }) }),
                /* @__PURE__ */ jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsx("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800", children: attendee.gender }) }),
                /* @__PURE__ */ jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: attendee.dob ? formatDate(attendee.dob) : "N/A" }) })
              ]
            },
            attendee.id || index
          );
        }) })
      ] }) })
    }
  );
}
function OrderDetail({ id }) {
  var _a, _b, _c;
  const { translate } = useTranslations();
  const {
    order,
    isLoading
    /* error not used */
  } = useOrderDetail(id);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("order.title", "My Orders") }),
    /* @__PURE__ */ jsx("div", { className: "py-6 min-h-screen bg-gray-50", children: /* @__PURE__ */ jsxs("div", { className: "container px-4 mx-auto max-w-7xl", children: [
      /* @__PURE__ */ jsx("div", { className: "flex items-center mb-6", children: /* @__PURE__ */ jsxs(
        Link,
        {
          href: route("my-account.orders"),
          className: "inline-flex items-center text-blue-600 transition-colors duration-200 hover:text-blue-800",
          children: [
            /* @__PURE__ */ jsx(
              "svg",
              {
                xmlns: "http://www.w3.org/2000/svg",
                className: "mr-2 w-5 h-5",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor",
                children: /* @__PURE__ */ jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M15 19l-7-7 7-7"
                  }
                )
              }
            ),
            translate("order.back", "Back to Orders")
          ]
        }
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
        /* @__PURE__ */ jsx("h1", { className: "mb-2 text-3xl font-bold text-gray-900", children: translate("order.detail_title", "Order Details") }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: translate(
          "order.subtitle",
          "Complete information about your order"
        ) })
      ] }),
      isLoading ? /* @__PURE__ */ jsxs("div", { className: "flex justify-center items-center py-12", children: [
        /* @__PURE__ */ jsx("div", { className: "w-12 h-12 rounded-full border-b-2 border-blue-600 animate-spin" }),
        /* @__PURE__ */ jsx("span", { className: "ml-3 text-gray-600", children: translate("common.loading", "Loading...") })
      ] }) : order ? /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
        /* @__PURE__ */ jsx(OrderSummary, { order }),
        ((_a = order.ticket) == null ? void 0 : _a.event) && /* @__PURE__ */ jsx(EventInfo, { event: order.ticket.event }),
        ((_c = (_b = order.ticket) == null ? void 0 : _b.event) == null ? void 0 : _c.stadium) && /* @__PURE__ */ jsx(
          StadiumInfo,
          {
            stadium: order.ticket.event.stadium
          }
        ),
        order.ticket && /* @__PURE__ */ jsx(TicketInfo, { ticket: order.ticket }),
        order.buyer && /* @__PURE__ */ jsx(BuyerInfo, { buyer: order.buyer }),
        order.combined_restrictions.length > 0 && /* @__PURE__ */ jsx(
          RestrictionsInfo,
          {
            restrictions: order.combined_restrictions
          }
        ),
        order.transactions.length > 0 && /* @__PURE__ */ jsx(
          TransactionInfo,
          {
            transactions: order.transactions
          }
        ),
        order.attendees.length > 0 && /* @__PURE__ */ jsx(AttendeesInfo, { attendees: order.attendees })
      ] }) : /* @__PURE__ */ jsxs("div", { className: "p-12 text-center bg-white rounded-xl border border-gray-200 shadow-sm", children: [
        /* @__PURE__ */ jsx("div", { className: "mb-4 text-gray-400", children: /* @__PURE__ */ jsx(
          "svg",
          {
            className: "mx-auto w-16 h-16",
            fill: "none",
            viewBox: "0 0 24 24",
            stroke: "currentColor",
            children: /* @__PURE__ */ jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 1,
                d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              }
            )
          }
        ) }),
        /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-medium text-gray-900", children: translate(
          "order.not_found",
          "Order not found"
        ) }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: translate(
          "order.not_found_description",
          "The order you're looking for doesn't exist or you don't have permission to view it."
        ) })
      ] })
    ] }) })
  ] });
}
OrderDetail.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "orders"
    }
  ) });
};
const __vite_glob_0_20 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: OrderDetail
}, Symbol.toStringTag, { value: "Module" }));
const fetchUserOrders = createAsyncThunk(
  "orders/fetchUserOrders",
  async ({
    url = route("api.orders.index"),
    filters,
    canAppendData = false
  }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendData, ...response.data };
    } else {
      throw new Error("Failed to fetch orders");
    }
  }
);
const initialState$6 = {
  orders: [],
  isLoading: true,
  nextPageUrl: null,
  filters: {
    search: "",
    status: "",
    date_from: "",
    date_to: ""
  }
};
const ordersSlice = createSlice({
  name: "orders",
  initialState: initialState$6,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$6.filters;
    },
    setFilterAtOnce: (state, action) => {
      state.filters = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchUserOrders.pending, (state) => {
      state.isLoading = true;
    }).addCase(fetchUserOrders.fulfilled, (state, action) => {
      const { orders, meta } = action.payload;
      if (action.payload.canAppendData) {
        const existingOrdersMap = new Map(
          state.orders.map((order) => [order.id, order])
        );
        const newOrders = orders.filter(
          (order) => !existingOrdersMap.has(order.id)
        );
        state.orders = [...state.orders, ...newOrders];
      } else {
        state.orders = orders;
      }
      state.isLoading = false;
      state.nextPageUrl = meta.next_page_url;
    }).addCase(fetchUserOrders.rejected, (state) => {
      state.orders = [];
      state.isLoading = false;
    });
  }
});
const { resetFilters: resetFilters$2, setFilterAtOnce } = ordersSlice.actions;
const ordersReducer = ordersSlice.reducer;
function useOrders() {
  const dispatch = useDispatch();
  const { orders, isLoading, nextPageUrl, filters } = useSelector(
    (state) => state.orders
  );
  const fetchOrders = async () => {
    dispatch(
      fetchUserOrders({
        url: route("api.orders.index"),
        filters
      })
    );
  };
  const clearFilters = () => {
    dispatch(resetFilters$2());
  };
  const setFilter2 = (key, value) => {
    dispatch(
      setFilterAtOnce({
        ...filters,
        [key]: value
      })
    );
  };
  const loadMoreOrders = () => {
    if (nextPageUrl) {
      dispatch(
        fetchUserOrders({
          url: nextPageUrl,
          filters,
          canAppendData: true
        })
      );
    }
  };
  return {
    orders,
    isLoading,
    nextPageUrl,
    filters,
    fetchOrders,
    clearFilters,
    setFilter: setFilter2,
    loadMoreOrders
  };
}
const OrderCard = forwardRef(({ order }, ref) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
  const { translate } = useTranslations();
  const getStatusBadgeClass = (color) => {
    switch (color) {
      case "success":
        return "bg-green-100 text-green-800 border border-green-200";
      case "warning":
        return "bg-yellow-100 text-yellow-800 border border-yellow-200";
      case "danger":
        return "bg-red-100 text-red-800 border border-red-200";
      case "info":
        return "bg-blue-100 text-blue-800 border border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border border-gray-200";
    }
  };
  return /* @__PURE__ */ jsxs(
    "div",
    {
      ref,
      className: "overflow-hidden bg-white rounded-lg border border-gray-200 shadow-md transition-all duration-300 hover:shadow-lg",
      children: [
        /* @__PURE__ */ jsx("div", { className: "p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100", children: /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex gap-3 items-center", children: [
            /* @__PURE__ */ jsxs("h2", { className: "text-lg font-bold text-gray-900", children: [
              translate("order.order_number", "Order"),
              " #",
              order.order_no
            ] }),
            /* @__PURE__ */ jsx(
              "span",
              {
                className: `px-2 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(order.status.color)}`,
                children: order.status.label
              }
            )
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "text-right", children: [
            /* @__PURE__ */ jsx("div", { className: "text-xl font-bold text-gray-900", children: formatCurrency(order.total_price, order.currency) }),
            /* @__PURE__ */ jsxs("div", { className: "text-xs text-gray-500", children: [
              order.quantity,
              " ",
              order.quantity === 1 ? translate("ticket.ticket_text", "ticket") : translate("ticket.tickets_text", "tickets")
            ] })
          ] })
        ] }) }),
        /* @__PURE__ */ jsxs("div", { className: "p-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
            /* @__PURE__ */ jsx("div", { className: "flex-shrink-0 w-32 h-24", children: /* @__PURE__ */ jsx("div", { className: "relative group", children: /* @__PURE__ */ jsx("figure", { className: "overflow-hidden relative w-full h-32 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg", children: ((_b = (_a = order.ticket.event) == null ? void 0 : _a.stadium) == null ? void 0 : _b.image) ? /* @__PURE__ */ jsx(
              "img",
              {
                src: order.ticket.event.stadium.image,
                alt: ((_d = (_c = order.ticket.event) == null ? void 0 : _c.stadium) == null ? void 0 : _d.image_alt) || translate(
                  "ticket.stadium_text",
                  "Stadium"
                ),
                className: "object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
              }
            ) : /* @__PURE__ */ jsx("div", { className: "flex justify-center items-center h-full", children: /* @__PURE__ */ jsx(
              "img",
              {
                className: "w-16 h-auto opacity-60",
                src: "/img/ticketgol-logo.png",
                alt: ((_f = (_e = order.ticket.event) == null ? void 0 : _e.stadium) == null ? void 0 : _f.name) || translate(
                  "ticket.stadium_text",
                  "Stadium"
                )
              }
            ) }) }) }) }),
            /* @__PURE__ */ jsxs("div", { className: "flex-1 space-y-3", children: [
              /* @__PURE__ */ jsx("div", { className: "p-3 mb-3 bg-blue-50 rounded-lg border border-blue-200", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-3 items-center", children: [
                /* @__PURE__ */ jsx(Ticket, { className: "w-5 h-5 text-blue-600" }),
                /* @__PURE__ */ jsxs("div", { className: "flex gap-4 items-center", children: [
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs font-medium tracking-wide text-blue-600 uppercase", children: translate(
                      "common.ticket_number",
                      "Ticket No"
                    ) }),
                    /* @__PURE__ */ jsxs("div", { className: "font-mono font-bold text-blue-900", children: [
                      "#",
                      order.ticket.ticket_no
                    ] })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { className: "pl-4 border-l border-blue-300", children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs font-medium tracking-wide text-blue-600 uppercase", children: translate("common.type", "Type") }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-blue-900", children: order.ticket.type || translate(
                      "common.e_ticket",
                      "E-Ticket"
                    ) })
                  ] })
                ] })
              ] }) }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-semibold text-gray-900", children: ((_g = order.ticket.event) == null ? void 0 : _g.name) || translate("common.not_available", "N/A") }),
                /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-3 text-sm md:grid-cols-2", children: [
                  ((_h = order.ticket.event) == null ? void 0 : _h.date) && /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center", children: [
                    /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4 text-gray-500" }),
                    /* @__PURE__ */ jsxs("span", { className: "text-gray-600", children: [
                      formatDate(
                        order.ticket.event.date
                      ),
                      ((_i = order.ticket.event) == null ? void 0 : _i.time) && /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
                        "at",
                        " ",
                        formatTime(
                          order.ticket.event.time
                        )
                      ] })
                    ] })
                  ] }),
                  ((_j = order.ticket.event) == null ? void 0 : _j.stadium) && /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center", children: [
                    /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 text-gray-500" }),
                    /* @__PURE__ */ jsx("span", { className: "text-gray-600", children: order.ticket.event.stadium.name })
                  ] })
                ] }),
                (((_k = order.ticket.event) == null ? void 0 : _k.home_club) || ((_l = order.ticket.event) == null ? void 0 : _l.guest_club)) && /* @__PURE__ */ jsx("div", { className: "pt-3 mt-3 border-t border-gray-200", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-4 justify-center items-center", children: [
                  ((_m = order.ticket.event) == null ? void 0 : _m.home_club) && /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
                    /* @__PURE__ */ jsx("div", { className: "mb-1 text-xs text-gray-600", children: translate(
                      "common.home",
                      "Home"
                    ) }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-blue-600", children: order.ticket.event.home_club })
                  ] }),
                  ((_n = order.ticket.event) == null ? void 0 : _n.home_club) && ((_o = order.ticket.event) == null ? void 0 : _o.guest_club) && /* @__PURE__ */ jsx("div", { className: "text-xl font-bold text-gray-400", children: "VS" }),
                  ((_p = order.ticket.event) == null ? void 0 : _p.guest_club) && /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
                    /* @__PURE__ */ jsx("div", { className: "mb-1 text-xs text-gray-600", children: translate(
                      "common.guest",
                      "Guest"
                    ) }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-red-600", children: order.ticket.event.guest_club })
                  ] })
                ] }) })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex gap-2 items-center text-sm text-gray-600", children: [
                /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4" }),
                /* @__PURE__ */ jsx("span", { children: order.purchase_date ? /* @__PURE__ */ jsxs(Fragment, { children: [
                  translate(
                    "order.purchased",
                    "Purchased"
                  ),
                  ": ",
                  formatDate(order.purchase_date)
                ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
                  translate(
                    "order.created_at",
                    "Created"
                  ),
                  ": ",
                  formatDate(order.created_at)
                ] }) })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex justify-between items-center pt-3 mt-3 border-t border-gray-200", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-3 items-center", children: [
            /* @__PURE__ */ jsxs(
              Link,
              {
                href: route("my-account.order-detail", {
                  id: order.id
                }),
                className: "inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md transition-colors duration-200 hover:bg-blue-700",
                children: [
                  /* @__PURE__ */ jsx(Eye, { className: "w-4 h-4" }),
                  translate("order.view_details", "View Details")
                ]
              }
            ),
            order.status.color === "success" && /* @__PURE__ */ jsxs("button", { className: "inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md transition-colors duration-200 hover:bg-gray-200", children: [
              /* @__PURE__ */ jsx(Download, { className: "w-4 h-4" }),
              translate(
                "order.download_ticket",
                "Download Ticket"
              )
            ] })
          ] }) })
        ] })
      ]
    }
  );
});
OrderCard.displayName = "OrderCard";
const useOrderStatuses = () => {
  const [statuses, setStatuses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const fetchStatuses = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await axios$1.get(route("api.orders.statuses"));
      if (response.data.success === true) {
        setStatuses(response.data.statuses);
      }
    } catch (err) {
      setError(err.message);
      console.error("Failed to fetch order statuses:", err);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchStatuses();
  }, []);
  return {
    statuses,
    isLoading,
    error,
    refetch: fetchStatuses
  };
};
const DatePicker = ({
  value,
  onChange,
  placeholder = "Select date",
  className = "",
  disabled = false,
  minDate = null,
  maxDate = null,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(dayjs(value || void 0));
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);
  const generateCalendarDays = () => {
    const startOfMonth = currentMonth.startOf("month");
    const endOfMonth = currentMonth.endOf("month");
    const startOfCalendar = startOfMonth.startOf("week");
    const endOfCalendar = endOfMonth.endOf("week");
    const days = [];
    let day = startOfCalendar;
    while (day.isBefore(endOfCalendar) || day.isSame(endOfCalendar, "day")) {
      days.push(day);
      day = day.add(1, "day");
    }
    return days;
  };
  const handleDateSelect = (date) => {
    const formattedDate = date.format("YYYY-MM-DD");
    onChange(formattedDate);
    setIsOpen(false);
  };
  const handlePrevMonth = () => {
    setCurrentMonth(currentMonth.subtract(1, "month"));
  };
  const handleNextMonth = () => {
    setCurrentMonth(currentMonth.add(1, "month"));
  };
  const isDateDisabled = (date) => {
    if (minDate && date.isBefore(dayjs(minDate), "day")) return true;
    if (maxDate && date.isAfter(dayjs(maxDate), "day")) return true;
    return false;
  };
  const formatDisplayValue = (dateValue) => {
    if (!dateValue) return "";
    return dayjs(dateValue).format("DD/MM/YYYY");
  };
  const calendarDays = generateCalendarDays();
  const selectedDate = value ? dayjs(value) : null;
  const { error, ...inputProps } = props;
  return /* @__PURE__ */ jsxs("div", { className: `relative ${className}`, ref: dropdownRef, children: [
    /* @__PURE__ */ jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          ref: inputRef,
          type: "text",
          value: formatDisplayValue(value),
          placeholder,
          readOnly: true,
          disabled,
          onClick: () => !disabled && setIsOpen(!isOpen),
          className: `
                        input input-bordered w-full pr-10 cursor-pointer
                        ${disabled ? "input-disabled" : ""}
                    `
        }
      ),
      /* @__PURE__ */ jsx(
        Calendar,
        {
          size: 18,
          className: `
                        absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none
                        ${disabled ? "text-gray-400" : "text-gray-500"}
                    `
        }
      )
    ] }),
    error && /* @__PURE__ */ jsx(InputError, { message: error, className: "mt-2" }),
    isOpen && !disabled && /* @__PURE__ */ jsxs("div", { className: "absolute top-full left-0 mt-1 z-50 bg-base-100 border border-base-300 rounded-lg shadow-lg p-4 w-80", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-4", children: [
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            onClick: handlePrevMonth,
            className: "btn btn-ghost btn-sm btn-square",
            children: /* @__PURE__ */ jsx(ChevronLeft, { size: 16 })
          }
        ),
        /* @__PURE__ */ jsx("h3", { className: "font-semibold text-base", children: currentMonth.format("MMMM YYYY") }),
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            onClick: handleNextMonth,
            className: "btn btn-ghost btn-sm btn-square",
            children: /* @__PURE__ */ jsx(ChevronRight, { size: 16 })
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "grid grid-cols-7 gap-1 mb-2", children: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map(
        (day) => /* @__PURE__ */ jsx(
          "div",
          {
            className: "text-center text-xs font-medium text-gray-500 py-2",
            children: day
          },
          day
        )
      ) }),
      /* @__PURE__ */ jsx("div", { className: "grid grid-cols-7 gap-1", children: calendarDays.map((day, index) => {
        const isCurrentMonth = day.isSame(
          currentMonth,
          "month"
        );
        const isSelected = selectedDate && day.isSame(selectedDate, "day");
        const isToday = day.isSame(dayjs(), "day");
        const isDisabled = isDateDisabled(day);
        return /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            onClick: () => !isDisabled && handleDateSelect(day),
            disabled: isDisabled,
            className: `
                                        w-8 h-8 text-sm rounded-md transition-colors
                                        ${!isCurrentMonth ? "text-gray-300" : ""}
                                        ${isSelected ? "bg-primary text-primary-content" : ""}
                                        ${isToday && !isSelected ? "bg-base-200 font-semibold" : ""}
                                        ${isDisabled ? "text-gray-300 cursor-not-allowed" : "hover:bg-base-200"}
                                        ${!isSelected && !isDisabled && isCurrentMonth ? "hover:bg-base-200" : ""}
                                    `,
            children: day.format("D")
          },
          index
        );
      }) }),
      value && /* @__PURE__ */ jsx("div", { className: "mt-4 pt-3 border-t border-base-300", children: /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          onClick: () => {
            onChange("");
            setIsOpen(false);
          },
          className: "btn btn-ghost btn-sm w-full",
          children: "Clear Date"
        }
      ) })
    ] })
  ] });
};
const OrderFilters = ({ filters, isLoading }) => {
  const { translate } = useTranslations();
  const { setFilter: setFilter2, fetchOrders, clearFilters } = useOrders();
  const { statuses: statusOptions, isLoading: statusesLoading } = useOrderStatuses();
  const handleSearch = (e) => {
    e.preventDefault();
    fetchOrders();
  };
  return /* @__PURE__ */ jsx("form", { onSubmit: handleSearch, children: /* @__PURE__ */ jsxs("div", { className: "p-4 mb-6 bg-white rounded-lg shadow", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-4 mb-4 md:flex-row", children: [
      /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          TextInput,
          {
            type: "text",
            value: filters.search,
            onChange: (e) => setFilter2("search", e.target.value),
            placeholder: translate(
              "orders.search",
              "Search orders..."
            ),
            className: "pr-10"
          }
        ),
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "submit",
            className: "absolute right-2 top-1/2 text-gray-500 transform -translate-y-1/2 hover:text-primary",
            children: /* @__PURE__ */ jsx(Search$1, { size: 18, className: "mr-3" })
          }
        )
      ] }) }),
      /* @__PURE__ */ jsx("div", { className: "w-full md:w-1/3", children: /* @__PURE__ */ jsx(
        SelectInput,
        {
          options: statusOptions,
          value: statusOptions.find(
            (opt) => opt.value === filters.status
          ),
          onChange: (option) => setFilter2("status", (option == null ? void 0 : option.value) || ""),
          placeholder: translate(
            "order.filter_by_status",
            "Filter by Status"
          ),
          isLoading: statusesLoading,
          menuPortalTarget: document.body
        }
      ) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-4 mb-4 md:flex-row", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
        /* @__PURE__ */ jsx("label", { className: "block mb-1 text-sm font-medium text-gray-700", children: translate("order.date_from", "Date From") }),
        /* @__PURE__ */ jsx(
          DatePicker,
          {
            value: filters.date_from,
            onChange: (value) => setFilter2("date_from", value),
            placeholder: translate(
              "order.select_date_from",
              "Select start date"
            ),
            maxDate: filters.date_to || void 0
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
        /* @__PURE__ */ jsx("label", { className: "block mb-1 text-sm font-medium text-gray-700", children: translate("order.date_to", "Date To") }),
        /* @__PURE__ */ jsx(
          DatePicker,
          {
            value: filters.date_to,
            onChange: (value) => setFilter2("date_to", value),
            placeholder: translate(
              "order.select_date_to",
              "Select end date"
            ),
            minDate: filters.date_from || void 0
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex gap-6 justify-end", children: [
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: clearFilters,
          className: "btn btn-outline",
          disabled: isLoading,
          children: translate("common.clear", "Clear")
        }
      ),
      /* @__PURE__ */ jsxs(
        "button",
        {
          type: "submit",
          className: "btn btn-primary",
          disabled: isLoading,
          children: [
            /* @__PURE__ */ jsx(Filter, { size: 16, className: "mr-1" }),
            translate("common.apply_filters", "Apply Filters")
          ]
        }
      )
    ] })
  ] }) });
};
function Orders() {
  const { translate } = useTranslations();
  const {
    orders,
    isLoading,
    fetchOrders,
    filters,
    nextPageUrl,
    loadMoreOrders
  } = useOrders();
  useEffect(() => {
    fetchOrders();
  }, []);
  const [infiniteRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreOrders,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("order.title", "My Orders") }),
    /* @__PURE__ */ jsx("div", { className: "py-3", children: /* @__PURE__ */ jsxs("div", { className: "container px-4 mx-auto", children: [
      /* @__PURE__ */ jsx("h1", { className: "mb-8 text-2xl font-bold", children: translate("order.title", "My Orders") }),
      /* @__PURE__ */ jsx(OrderFilters, { filters, loading: isLoading }),
      orders.length === 0 && !isLoading ? /* @__PURE__ */ jsx("div", { className: "p-6 mt-4 text-center bg-gray-50 rounded-lg border", children: /* @__PURE__ */ jsx("p", { children: translate(
        "order.no_orders",
        "You don't have any orders yet."
      ) }) }) : /* @__PURE__ */ jsxs("div", { className: "space-y-4 max-h-[70vh] overflow-y-auto pr-2 rounded-lg mt-4", children: [
        orders.map((order, index) => {
          const isLastElement = index === orders.length - 1;
          return /* @__PURE__ */ jsx(
            OrderCard,
            {
              order,
              ref: isLastElement ? infiniteRef : null
            },
            order.id
          );
        }),
        isLoading && /* @__PURE__ */ jsx("div", { className: "py-4 text-center", children: /* @__PURE__ */ jsx(
          "div",
          {
            className: "inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",
            role: "status",
            children: /* @__PURE__ */ jsx("span", { className: "!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]", children: translate(
              "common.loading",
              "Loading..."
            ) })
          }
        ) })
      ] })
    ] }) })
  ] });
}
Orders.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "orders"
    }
  ) });
};
const __vite_glob_0_21 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Orders
}, Symbol.toStringTag, { value: "Module" }));
function DangerButton({
  className = "",
  disabled,
  children,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    "button",
    {
      ...props,
      className: `inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white transition duration-150 ease-in-out hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 active:bg-red-700 ${disabled && "opacity-25"} ` + className,
      disabled,
      children
    }
  );
}
function Modal({
  children,
  show = false,
  maxWidth = "2xl",
  closeable = true,
  onClose = () => {
  }
}) {
  const close = () => {
    if (closeable) {
      onClose();
    }
  };
  const maxWidthClass = {
    sm: "sm:max-w-sm",
    md: "sm:max-w-md",
    lg: "sm:max-w-lg",
    xl: "sm:max-w-xl",
    "2xl": "sm:max-w-2xl"
  }[maxWidth];
  return /* @__PURE__ */ jsx(Transition, { show, leave: "duration-200", children: /* @__PURE__ */ jsxs(
    Dialog,
    {
      as: "div",
      id: "modal",
      className: "fixed inset-0 z-50 flex transform items-center overflow-y-auto px-4 py-6 transition-all sm:px-0",
      onClose: close,
      children: [
        /* @__PURE__ */ jsx(
          TransitionChild,
          {
            enter: "ease-out duration-300",
            enterFrom: "opacity-0",
            enterTo: "opacity-100",
            leave: "ease-in duration-200",
            leaveFrom: "opacity-100",
            leaveTo: "opacity-0",
            children: /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-gray-500/75" })
          }
        ),
        /* @__PURE__ */ jsx(
          TransitionChild,
          {
            enter: "ease-out duration-300",
            enterFrom: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
            enterTo: "opacity-100 translate-y-0 sm:scale-100",
            leave: "ease-in duration-200",
            leaveFrom: "opacity-100 translate-y-0 sm:scale-100",
            leaveTo: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
            children: /* @__PURE__ */ jsx(
              DialogPanel,
              {
                className: `mb-6 transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:mx-auto sm:w-full ${maxWidthClass}`,
                children
              }
            )
          }
        )
      ]
    }
  ) });
}
function SecondaryButton({
  type = "button",
  className = "",
  disabled,
  children,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    "button",
    {
      ...props,
      type,
      className: `inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-700 shadow-sm transition duration-150 ease-in-out hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 ${disabled && "opacity-25"} ` + className,
      disabled,
      children
    }
  );
}
function DeleteUserForm() {
  const { translate } = useTranslations();
  const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
  const passwordInput = useRef();
  const {
    data,
    setData,
    delete: destroy,
    processing,
    reset,
    errors,
    clearErrors
  } = useForm({
    password: ""
  });
  const confirmUserDeletion = () => {
    setConfirmingUserDeletion(true);
  };
  const deleteUser = (e) => {
    e.preventDefault();
    destroy(route("profile.destroy"), {
      preserveScroll: true,
      onSuccess: () => {
        closeModal();
        toast.success(translate("common.deleted_text"));
      },
      onError: () => {
        passwordInput.current.focus();
        toast.error(errors.password);
      },
      onFinish: () => reset()
    });
  };
  const closeModal = () => {
    setConfirmingUserDeletion(false);
    clearErrors();
    reset();
  };
  return /* @__PURE__ */ jsxs("section", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxs("header", { children: [
      /* @__PURE__ */ jsx("h2", { className: "text-lg font-medium text-gray-900", children: translate("profile.delete_account") }),
      /* @__PURE__ */ jsx("p", { className: "mt-1 text-sm text-gray-600", children: translate("profile.delete_account_description") })
    ] }),
    /* @__PURE__ */ jsx(DangerButton, { onClick: confirmUserDeletion, children: translate("profile.delete_account") }),
    /* @__PURE__ */ jsx(Modal, { show: confirmingUserDeletion, onClose: closeModal, children: /* @__PURE__ */ jsxs("form", { onSubmit: deleteUser, className: "p-6", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-lg font-medium text-gray-900", children: translate("profile.delete_confirm_message") }),
      /* @__PURE__ */ jsx("p", { className: "mt-1 text-sm text-gray-600", children: translate("profile.delete_confirm_description") }),
      /* @__PURE__ */ jsx("div", { className: "mt-6", children: /* @__PURE__ */ jsx(
        PasswordInput,
        {
          id: "password",
          ref: passwordInput,
          value: data.password,
          onChange: (e) => setData("password", e.target.value),
          isFocused: true,
          placeholder: translate(
            "common.placeholder.password"
          ),
          autoComplete: "current-password",
          error: errors.password
        }
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "mt-6 flex justify-end", children: [
        /* @__PURE__ */ jsx(SecondaryButton, { onClick: closeModal, children: translate("common.cancel_btn") }),
        /* @__PURE__ */ jsx(DangerButton, { className: "ms-3", disabled: processing, children: translate("profile.delete_account") })
      ] })
    ] }) })
  ] });
}
const __vite_glob_0_23 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: DeleteUserForm
}, Symbol.toStringTag, { value: "Module" }));
function UpdatePasswordForm() {
  const { translate } = useTranslations();
  const passwordInput = useRef();
  const currentPasswordInput = useRef();
  const {
    data,
    setData,
    errors,
    put,
    reset,
    processing,
    recentlySuccessful
  } = useForm({
    current_password: "",
    password: "",
    password_confirmation: ""
  });
  const updatePassword = (e) => {
    e.preventDefault();
    put(route("password.update"), {
      preserveScroll: true,
      onSuccess: () => {
        reset();
        toast.success(translate("common.saved_text"));
      },
      onError: (errors2) => {
        if (errors2.password) {
          reset("password", "password_confirmation");
          passwordInput.current.focus();
          toast.error(errors2.password);
        }
        if (errors2.current_password) {
          reset("current_password");
          currentPasswordInput.current.focus();
          toast.error(errors2.current_password);
        }
      }
    });
  };
  return /* @__PURE__ */ jsxs("section", { children: [
    /* @__PURE__ */ jsx("header", { children: /* @__PURE__ */ jsx("h2", { className: "text-lg font-medium text-gray-900", children: translate("profile.update_password") }) }),
    /* @__PURE__ */ jsxs("form", { onSubmit: updatePassword, className: "mt-6 space-y-6", children: [
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 sm:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsx(
          PasswordInput,
          {
            id: "current_password",
            name: "current_password",
            ref: currentPasswordInput,
            value: data.current_password,
            onChange: (e) => setData("current_password", e.target.value),
            placeholder: translate(
              "common.placeholder.current_password"
            ),
            autoComplete: "current-password",
            error: errors.current_password
          }
        ),
        /* @__PURE__ */ jsx(
          PasswordInput,
          {
            id: "password",
            name: "password",
            ref: passwordInput,
            value: data.password,
            onChange: (e) => setData("password", e.target.value),
            placeholder: translate(
              "common.placeholder.new_password"
            ),
            autoComplete: "new-password",
            error: errors.password
          }
        ),
        /* @__PURE__ */ jsx(
          PasswordInput,
          {
            id: "password_confirmation",
            name: "password_confirmation",
            value: data.password_confirmation,
            onChange: (e) => setData("password_confirmation", e.target.value),
            placeholder: translate(
              "common.placeholder.password_confirmation"
            ),
            autoComplete: "new-password",
            error: errors.password_confirmation
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-end gap-4", children: [
        /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("common.save_btn") }),
        /* @__PURE__ */ jsx(
          Transition,
          {
            show: recentlySuccessful,
            enter: "transition ease-in-out",
            enterFrom: "opacity-0",
            leave: "transition ease-in-out",
            leaveTo: "opacity-0",
            children: /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-600", children: translate("common.saved_text") })
          }
        )
      ] })
    ] })
  ] });
}
const __vite_glob_0_24 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: UpdatePasswordForm
}, Symbol.toStringTag, { value: "Module" }));
function UpdateProfileInformation({
  mustVerifyEmail,
  status,
  countries,
  genders
}) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i;
  const user = usePage().props.auth.user;
  const { translate } = useTranslations();
  const { data, setData, patch, errors, processing, recentlySuccessful } = useForm({
    name: user.name || "",
    user_name: user.user_name || "",
    email: user.email || "",
    surname: ((_a = user.user_detail) == null ? void 0 : _a.surname) || "",
    phone: ((_b = user.user_detail) == null ? void 0 : _b.phone) || "",
    gender: ((_c = user.user_detail) == null ? void 0 : _c.gender) || "",
    address: ((_d = user.user_detail) == null ? void 0 : _d.address) || "",
    city: ((_e = user.user_detail) == null ? void 0 : _e.city) || "",
    country_id: parseInt((_f = user.user_detail) == null ? void 0 : _f.country_id) || "",
    zip: ((_g = user.user_detail) == null ? void 0 : _g.zip) || "",
    company: ((_h = user.user_detail) == null ? void 0 : _h.company) || "",
    government_id: ((_i = user.user_detail) == null ? void 0 : _i.government_id) || ""
  });
  const genderOptions = Object.entries(genders).map(([key, value]) => ({
    value: key,
    label: value
  }));
  const countryOptions = countries.map((country) => ({
    value: country.id,
    label: country.translation.name
  }));
  const submit = (e) => {
    e.preventDefault();
    patch(route("profile.update"), {
      preserveScroll: true,
      onError: (errors2) => {
        toast.error(Object.values(errors2)[0]);
      },
      onSuccess: () => {
        toast.success(translate("common.saved_text"));
      }
    });
  };
  return /* @__PURE__ */ jsxs("section", { children: [
    /* @__PURE__ */ jsx("header", { children: /* @__PURE__ */ jsx("h2", { className: "text-lg font-medium text-gray-900", children: translate("profile.page_sub_title") }) }),
    /* @__PURE__ */ jsxs("form", { onSubmit: submit, className: "mt-4", children: [
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "name",
            name: "name",
            value: data.name,
            placeholder: translate("common.placeholder.name"),
            label: translate("common.labels.name"),
            isFocused: true,
            datarequired: "true",
            onChange: (e) => setData("name", e.target.value),
            error: errors.name
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "surname",
            surname: "surname",
            value: data.surname,
            placeholder: translate("common.placeholder.surname"),
            label: translate("common.labels.surname"),
            datarequired: "true",
            onChange: (e) => setData("surname", e.target.value),
            error: errors.surname
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "user_name",
            name: "user_name",
            value: data.user_name,
            placeholder: translate("common.placeholder.user_name"),
            label: translate("common.labels.user_name"),
            datarequired: "true",
            onChange: (e) => setData("user_name", e.target.value),
            error: errors.user_name
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "email",
            type: "email",
            name: "email",
            value: data.email,
            autoComplete: "username",
            placeholder: translate("common.placeholder.email"),
            label: translate("common.labels.email"),
            datarequired: "true",
            onChange: (e) => setData("email", e.target.value),
            error: errors.email
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "phone",
            name: "phone",
            type: "tel",
            value: data.phone,
            placeholder: translate("common.placeholder.phone"),
            label: translate("common.labels.phone"),
            datarequired: "true",
            maxLength: "15",
            onChange: (e) => setData("phone", e.target.value),
            error: errors.phone
          }
        ),
        /* @__PURE__ */ jsx(
          SelectInput,
          {
            id: "gender",
            name: "gender",
            placeholder: translate("common.placeholder.gender"),
            label: translate("common.labels.gender"),
            datarequired: "true",
            options: genderOptions,
            value: genderOptions.find(
              (option) => option.value === data.gender
            ),
            onChange: (selectedOption) => setData("gender", selectedOption.value),
            error: errors.gender
          }
        ),
        user.user_type === "broker" && /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "company",
            name: "company",
            value: data.company,
            placeholder: translate(
              "common.placeholder.company"
            ),
            label: translate("common.labels.company"),
            datarequired: "true",
            onChange: (e) => setData("company", e.target.value),
            error: errors.company
          }
        ),
        user.user_type === "broker" && /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "government_id",
            name: "government_id",
            value: data.government_id,
            placeholder: translate(
              "common.placeholder.government_id"
            ),
            label: translate("common.labels.government_id"),
            datarequired: "true",
            onChange: (e) => setData("government_id", e.target.value),
            error: errors.government_id
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "address",
            name: "address",
            value: data.address,
            placeholder: translate("common.placeholder.address"),
            label: translate("common.labels.address"),
            datarequired: "true",
            onChange: (e) => setData("address", e.target.value),
            error: errors.address
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "city",
            name: "city",
            value: data.city,
            placeholder: translate("common.placeholder.city"),
            label: translate("common.labels.city"),
            datarequired: "true",
            onChange: (e) => setData("city", e.target.value),
            error: errors.city
          }
        ),
        /* @__PURE__ */ jsx(
          SelectInput,
          {
            id: "country_id",
            name: "country_id",
            placeholder: translate("common.placeholder.country"),
            label: translate("common.labels.country"),
            datarequired: "true",
            options: countryOptions,
            value: countryOptions.find(
              (option) => option.value === data.country_id
            ),
            onChange: (selectedOption) => setData("country_id", selectedOption.value),
            error: errors.country_id
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "zip",
            name: "zip",
            value: data.zip,
            placeholder: translate("common.placeholder.zip"),
            label: translate("common.labels.zip"),
            datarequired: "true",
            onChange: (e) => setData("zip", e.target.value),
            error: errors.zip
          }
        )
      ] }),
      mustVerifyEmail && user.email_verified_at === null && /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsxs("p", { className: "mt-2 text-sm text-gray-800", children: [
          translate("profile.email_unverified_text"),
          /* @__PURE__ */ jsx(
            Link,
            {
              href: route("verification.send"),
              method: "post",
              as: "button",
              className: "rounded-md text-sm text-gray-600 underline ml-2 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",
              children: translate("profile.resend_link_text")
            }
          )
        ] }),
        status === "verification-link-sent" && /* @__PURE__ */ jsx("div", { className: "mt-2 text-sm font-medium text-green-600", children: translate("profile.new_link_text") })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "mt-4 flex items-center justify-end gap-4", children: [
        /* @__PURE__ */ jsx(PrimaryButton, { disabled: processing, children: translate("common.save_btn") }),
        /* @__PURE__ */ jsx(
          Transition,
          {
            show: recentlySuccessful,
            enter: "transition ease-in-out",
            enterFrom: "opacity-0",
            leave: "transition ease-in-out",
            leaveTo: "opacity-0",
            children: /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-600", children: translate("common.saved_text") })
          }
        )
      ] })
    ] })
  ] });
}
const __vite_glob_0_25 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: UpdateProfileInformation
}, Symbol.toStringTag, { value: "Module" }));
function Edit$1({ mustVerifyEmail, status, countries, genders }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      Head,
      {
        title: translate("common.menu.my_account.profile", "Profile")
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "py-3", children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-2xl font-bold mb-8", children: translate("profile.page_title", "Profile") }),
      /* @__PURE__ */ jsx("div", { className: "bg-white p-4 shadow rounded-lg sm:p-8 mb-6", children: /* @__PURE__ */ jsx(
        UpdateProfileInformation,
        {
          mustVerifyEmail,
          status,
          countries,
          genders
        }
      ) }),
      /* @__PURE__ */ jsx("div", { className: "bg-white p-4 shadow rounded-lg sm:p-8 mb-6", children: /* @__PURE__ */ jsx(UpdatePasswordForm, {}) }),
      /* @__PURE__ */ jsx("div", { className: "bg-white p-4 shadow rounded-lg sm:p-8 mb-6", children: /* @__PURE__ */ jsx(DeleteUserForm, {}) })
    ] }) })
  ] });
}
Edit$1.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "profile"
    }
  ) });
};
const __vite_glob_0_22 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Edit$1
}, Symbol.toStringTag, { value: "Module" }));
function formatMessage(message, replacements = {}) {
  let formatted = message;
  for (const key in replacements) {
    const regex = new RegExp(`:${key}`, "g");
    formatted = formatted.replace(regex, replacements[key]);
  }
  return formatted;
}
function humanizeFieldName(fieldName) {
  return fieldName.replace(/\.\d+\./g, ".").replace(/\./g, " ").replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
}
function getValidationRules(translate) {
  return {
    required: (value, formData, fieldName) => {
      if (value === void 0 || value === null || value === "") {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.required",
          "The :attribute field is required."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      return null;
    },
    email: (value, formData, fieldName) => {
      if (!value) return null;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.email",
          "The :attribute must be a valid email address."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      return null;
    },
    number: (value, formData, fieldName) => {
      if (!value) return null;
      if (isNaN(value)) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.numeric",
          "The :attribute must be a number."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      return null;
    },
    min: (min) => (value, formData, fieldName) => {
      if (!value) return null;
      if (Number(value) < min) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.min.numeric",
          "The :attribute must be at least :min."
        );
        return formatMessage(msgTemplate, { attribute: label, min });
      }
      return null;
    },
    max: (max) => (value, formData, fieldName) => {
      if (!value) return null;
      if (Number(value) > max) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.max.numeric",
          "The :attribute may not be greater than :max."
        );
        return formatMessage(msgTemplate, { attribute: label, max });
      }
      return null;
    },
    maxlength: (max) => (value, formData, fieldName) => {
      if (typeof value === "string" && value.length > max) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.max.string",
          "The :attribute may not be greater than :max characters."
        );
        return formatMessage(msgTemplate, { attribute: label, max });
      }
      return null;
    },
    minlength: (min) => (value, formData, fieldName) => {
      if (typeof value === "string" && value.length < min) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.min.string",
          "The :attribute must be at least :min characters."
        );
        return formatMessage(msgTemplate, { attribute: label, min });
      }
      return null;
    },
    date: (value, formData, fieldName) => {
      if (!value) return null;
      if (isNaN(Date.parse(value))) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.date",
          "The :attribute is not a valid date."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      return null;
    },
    greaterThan: (fieldToCompare) => (value, formData, fieldName) => {
      if (!value || !formData[fieldToCompare]) return null;
      if (value <= formData[fieldToCompare]) {
        const label = humanizeFieldName(fieldName);
        const compareLabel = humanizeFieldName(fieldToCompare);
        const msgTemplate = translate(
          "validation.greater_than",
          "The :attribute must be greater than :other."
        );
        return formatMessage(msgTemplate, {
          attribute: label,
          other: compareLabel
        });
      }
      return null;
    },
    maxDate: (maxDate) => (value, formData, fieldName) => {
      if (!value) return null;
      const selectedDate = dayjs(value).startOf("day");
      dayjs().startOf("day");
      if (selectedDate.isAfter(maxDate)) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.before",
          "The :attribute must be before :date."
        );
        return formatMessage(msgTemplate, {
          attribute: label,
          date: dayjs(maxDate).format("DD/MM/YYYY")
        });
      }
      return null;
    },
    array: (value, formData, fieldName) => {
      if (!Array.isArray(value)) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.array",
          "The :attribute must be an array."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      return null;
    },
    minArrayLength: (min) => (value, formData, fieldName) => {
      if (!Array.isArray(value)) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.array",
          "The :attribute must be an array."
        );
        return formatMessage(msgTemplate, { attribute: label });
      }
      if (value.length < min) {
        const label = humanizeFieldName(fieldName);
        const msgTemplate = translate(
          "validation.min.array",
          "The :attribute must have at least :min items."
        );
        return formatMessage(msgTemplate, { attribute: label, min });
      }
      return null;
    },
    equalsField: (fieldToCompare) => (value, formData, fieldName) => {
      if (value !== formData[fieldToCompare]) {
        const label = humanizeFieldName(fieldName);
        const compareLabel = humanizeFieldName(fieldToCompare);
        const msgTemplate = translate(
          "validation.confirmed",
          "The :attribute confirmation does not match."
        );
        return formatMessage(msgTemplate, {
          attribute: label,
          other: compareLabel
        });
      }
      return null;
    }
  };
}
function validateForm(formData, schema) {
  const errors = {};
  for (const field in schema) {
    const rules = schema[field];
    const wildcardMatch = field.match(/^(.+)\.\*\.(.+)$/);
    if (wildcardMatch) {
      const arrayField = wildcardMatch[1];
      const subField = wildcardMatch[2];
      const arrayValue = formData[arrayField];
      if (Array.isArray(arrayValue)) {
        arrayValue.forEach((item, index) => {
          const fullField = `${arrayField}.${index}.${subField}`;
          const value = item == null ? void 0 : item[subField];
          for (const rule of rules) {
            const error = rule(value, formData, fullField);
            if (error) {
              errors[fullField] = error;
              break;
            }
          }
        });
      }
    } else {
      const value = formData[field];
      for (const rule of rules) {
        const error = rule(value, formData, field);
        if (error) {
          errors[field] = error;
          break;
        }
      }
    }
  }
  return errors;
}
function useValidation(schema) {
  const { translate } = useTranslations();
  const rules = getValidationRules(translate);
  const [errors, setErrors] = useState({});
  function resolveSchema() {
    const resolved = {};
    for (const field in schema) {
      resolved[field] = schema[field].map((rule) => {
        if (typeof rule === "function") {
          return rule;
        }
        if (typeof rule === "string" && rules[rule]) {
          return rules[rule];
        }
        if (typeof rule === "object" && rule.rule && rules[rule.rule]) {
          return rules[rule.rule](rule.value);
        }
        console.log(`Unknown validation rule:`, rule);
        return () => null;
      });
    }
    return resolved;
  }
  function validate(formData) {
    const resolvedSchema = resolveSchema();
    const validationErrors = validateForm(formData, resolvedSchema);
    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  }
  function validateField(fieldName, fieldValue, formData) {
    const fieldRules = findMatchingSchemaRule(fieldName);
    if (!fieldRules) return;
    const resolvedFieldRules = fieldRules.map((rule) => {
      if (typeof rule === "function") {
        return rule;
      }
      if (typeof rule === "string" && rules[rule]) {
        return rules[rule];
      }
      if (typeof rule === "object" && rule.rule && rules[rule.rule]) {
        return rules[rule.rule](rule.value);
      }
      console.log(
        `Unknown validation rule for field "${fieldName}":`,
        rule
      );
      return () => null;
    });
    const value = fieldValue ?? getValueByPath(formData, fieldName);
    for (const ruleFn of resolvedFieldRules) {
      const error = ruleFn(value, formData, fieldName);
      if (error) {
        setErrors((prev) => ({ ...prev, [fieldName]: error }));
        return error;
      }
    }
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
    return null;
  }
  function getValueByPath(obj, path) {
    return path.split(".").reduce(
      (o, key) => o && o[key] !== void 0 ? o[key] : void 0,
      obj
    );
  }
  function findMatchingSchemaRule(fieldName) {
    for (const key in schema) {
      if (key.includes("*")) {
        const pattern = new RegExp(
          "^" + key.replace(/\./g, "\\.").replace("*", "\\d+") + "$"
        );
        if (pattern.test(fieldName)) {
          return schema[key];
        }
      } else if (key === fieldName) {
        return schema[key];
      }
    }
    return null;
  }
  return {
    errors,
    validate,
    validateField,
    setErrors
  };
}
const fetchTicketDetail = createAsyncThunk(
  "ticket/fetchTicketDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.ticket;
    } else {
      throw new Error("Failed to fetch ticket detail");
    }
  }
);
const initialState$5 = {
  ticket: null,
  ticketLoading: true,
  formData: {
    ticket_id: "",
    quantity: 1,
    price: "",
    face_value_price: "",
    currency_code: "EUR",
    ticket_rows: "",
    ticket_seats: "",
    quantity_split_type: "any",
    ticket_type: "",
    description: "",
    restrictions: [],
    terms_agreed: false
  }
};
const editTicketSlice = createSlice({
  name: "editTicket",
  initialState: initialState$5,
  reducers: {
    setFormData: (state, action) => {
      const { key, value } = action.payload;
      if (key === "restrictions") {
        if (state.formData.restrictions.includes(Number(value))) {
          state.formData.restrictions = state.formData.restrictions.filter(
            (item) => item !== Number(value)
          );
        } else {
          state.formData.restrictions.push(Number(value));
        }
      } else {
        state.formData[key] = Number(value);
      }
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchTicketDetail.pending, (state) => {
      state.ticketLoading = true;
    }).addCase(fetchTicketDetail.fulfilled, (state, action) => {
      const ticket = action.payload;
      state.ticket = ticket;
      state.formData = {
        ticket_id: ticket.id || "",
        quantity: ticket.quantity || 1,
        price: ticket.price || "",
        face_value_price: ticket.face_value_price || "",
        currency_code: ticket.currency_code || "EUR",
        ticket_rows: ticket.ticket_rows || "",
        ticket_seats: ticket.ticket_seats || "",
        quantity_split_type: ticket.quantity_split_type || "any",
        ticket_type: ticket.ticket_type.value || "",
        description: ticket.description || "",
        restrictions: ticket.selectedRestrictions || [],
        terms_agreed: false
      };
      state.ticketLoading = false;
    }).addCase(fetchTicketDetail.rejected, (state) => {
      state.ticket = null;
      state.ticketLoading = false;
    });
  }
});
const { setFormData: setFormData$1 } = editTicketSlice.actions;
const editTicketReducer = editTicketSlice.reducer;
function useEditTicket() {
  const { ticket, ticketLoading, formData } = useSelector(
    (state) => state.editTicket
  );
  const dispatch = useDispatch();
  const getTicketDetail = (ticketNo) => {
    dispatch(
      fetchTicketDetail({
        url: route("api.tickets.show", { ticketNo })
      })
    );
  };
  const updateFormData = (key, value) => {
    dispatch(setFormData$1({ key, value }));
  };
  return {
    ticket,
    ticketLoading,
    getTicketDetail,
    formData,
    updateFormData
  };
}
const RadioInput = forwardRef(function RadioInput2({ options = [], className = "", checked, ...props }, ref) {
  useRef(null);
  const { error, ...radioProps } = props;
  return /* @__PURE__ */ jsxs("div", { className: `form-control mt-2 ${className}`, children: [
    props.label && /* @__PURE__ */ jsx(
      InputLabel,
      {
        htmlFor: props.name,
        value: props.label,
        className: props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""
      }
    ),
    options.map((option, index) => /* @__PURE__ */ jsxs(
      "label",
      {
        className: "flex items-center cursor-pointer mb-3",
        children: [
          /* @__PURE__ */ jsx(
            "input",
            {
              type: "radio",
              value: option.value,
              checked: checked === option.value,
              className: "radio radio-primary border-gray-300 hover:border-gray-500 checked:border-gray-500 radio-sm " + className,
              ...radioProps
            }
          ),
          /* @__PURE__ */ jsx("span", { className: "label-text ml-2 text", children: option.label })
        ]
      },
      option.value
    )),
    error && /* @__PURE__ */ jsx(InputError, { message: error, className: "mt-2" })
  ] });
});
const TicketForm = () => {
  const { translate } = useTranslations();
  const { ticket, formData, updateFormData } = useEditTicket();
  const validationSchema2 = {
    quantity: [
      "required",
      "number",
      { rule: "min", value: ticket.reservations_sum_quantity || 1 },
      {
        rule: "max",
        value: ticket.configurations.max_quantity_per_ticket
      }
    ],
    price: [
      "required",
      "number",
      { rule: "min", value: 1 },
      { rule: "max", value: ticket.configurations.max_price_limit }
    ],
    face_value_price: [
      "required",
      "number",
      { rule: "min", value: 1 },
      { rule: "max", value: ticket.configurations.max_price_limit }
    ],
    ticket_rows: [{ rule: "maxlength", value: 100 }],
    ticket_seats: [{ rule: "maxlength", value: 200 }],
    ticket_type: ["required"],
    quantity_split_type: ["required"],
    description: ["required", { rule: "maxlength", value: 255 }]
  };
  const { errors, validate, validateField } = useValidation(validationSchema2);
  const ticketTypeOptions = prepareOptionsFromEnum(
    translate("enums.ticket_types")
  );
  const ticketSplitTypeOptions = prepareOptionsFromEnum(
    translate("enums.ticket_quantity_split_types")
  );
  const updateTooltipMsg = translate(
    "sell.quantity_update_message"
  ).replaceAll("{{quantities}}", ticket.reservations_sum_quantity);
  const handleUpdateClick = async (e) => {
    var _a, _b, _c, _d;
    e.preventDefault();
    const isValid = validate(formData);
    if (!isValid) return;
    if (!formData.terms_agreed) {
      toast.error(
        translate(
          "sell.conditions_terms",
          "Please read and agree to our terms and conditions"
        )
      );
      return;
    }
    try {
      const { data } = await axios.post(
        route("api.tickets.update"),
        formData
      );
      if (data.success) {
        toast.success(data.message);
        router.visit(route("my-account.tickets"));
      }
    } catch (error) {
      if (((_a = error.response) == null ? void 0 : _a.status) === 422 && ((_b = error.response.data) == null ? void 0 : _b.errors)) {
        toast.error((_d = Object.values((_c = error.response.data) == null ? void 0 : _c.errors)[0]) == null ? void 0 : _d[0]);
      } else {
        toast.error(translate("common.something_wrong"));
      }
    }
  };
  return /* @__PURE__ */ jsxs("form", { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-lg font-bold border-t pt-3", children: translate("sell.step_1_title") }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-2 gap-x-4", children: [
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: "quantity",
            type: "number",
            value: formData.quantity,
            min: ticket.reservations_sum_quantity || 1,
            max: ticket.configurations.max_quantity_per_ticket,
            label: translate("sell.labels.quantity"),
            placeholder: translate("sell.placeholder.quantity"),
            datarequired: "true",
            onChange: (e) => updateFormData("quantity", e.target.value),
            error: errors == null ? void 0 : errors.quantity
          }
        ),
        ticket.reservations_sum_quantity > 0 && /* @__PURE__ */ jsx(
          "div",
          {
            className: "tooltip tooltip-left absolute top-0 right-0 mt-5 sm:mr-2",
            "data-tip": updateTooltipMsg,
            children: /* @__PURE__ */ jsx("span", { className: "text-gray-500 text-sm", children: /* @__PURE__ */ jsx(Info, { className: "w-4 h-4" }) })
          }
        )
      ] }),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "price",
          type: "number",
          value: formData.price,
          label: translate("sell.labels.price"),
          min: "0",
          max: ticket.configurations.max_price_limit,
          step: "0.01",
          placeholder: translate("sell.placeholder.price"),
          datarequired: "true",
          onChange: (e) => updateFormData("price", e.target.value),
          onBlur: (e) => {
            updateFormData(
              "price",
              parseFloat(e.target.value).toFixed(2)
            );
            validateField("price", formData.price, formData);
          },
          error: errors == null ? void 0 : errors.price
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "face_value_price",
          type: "number",
          value: formData.face_value_price,
          label: translate("sell.labels.face_value_price"),
          min: "0",
          max: ticket.configurations.max_price_limit,
          step: "0.01",
          placeholder: translate("sell.placeholder.face_value_price"),
          datarequired: "true",
          onChange: (e) => updateFormData("face_value_price", e.target.value),
          onBlur: (e) => {
            updateFormData(
              "face_value_price",
              parseFloat(e.target.value).toFixed(2)
            );
            validateField(
              "face_value_price",
              formData.face_value_price,
              formData
            );
          },
          error: errors == null ? void 0 : errors.face_value_price
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "ticket_rows",
          value: formData.ticket_rows,
          label: translate("sell.labels.ticket_rows"),
          placeholder: translate("sell.placeholder.ticket_rows"),
          onChange: (e) => updateFormData("ticket_rows", e.target.value),
          onBlur: (e) => validateField(
            "ticket_rows",
            formData.ticket_rows,
            formData
          ),
          error: errors == null ? void 0 : errors.ticket_rows
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "ticket_seats",
          value: formData.ticket_seats,
          label: translate("sell.labels.ticket_seats"),
          placeholder: translate("sell.placeholder.ticket_seats"),
          onChange: (e) => updateFormData("ticket_seats", e.target.value),
          onBlur: (e) => validateField(
            "ticket_seats",
            formData.ticket_seats,
            formData
          ),
          error: errors == null ? void 0 : errors.ticket_seats
        }
      )
    ] }),
    /* @__PURE__ */ jsx("h2", { className: "text-lg font-bold mt-5 border-t pt-3", children: translate("sell.step_2_title") }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-2", children: [
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "description",
          type: "textarea",
          value: formData.description,
          label: translate("sell.labels.description"),
          placeholder: translate("sell.placeholder.description"),
          datarequired: "true",
          onChange: (e) => updateFormData("description", e.target.value),
          onBlur: (e) => validateField(
            "description",
            formData.description,
            formData
          ),
          error: errors == null ? void 0 : errors.description
        }
      ),
      /* @__PURE__ */ jsx(
        RadioInput,
        {
          label: translate("sell.labels.ticket_type"),
          checked: formData.ticket_type,
          onChange: (e) => {
            updateFormData("ticket_type", e.target.value);
            validateField("ticket_type", e.target.value, formData);
          },
          datarequired: "true",
          options: ticketTypeOptions,
          error: errors == null ? void 0 : errors.ticket_type
        }
      ),
      /* @__PURE__ */ jsx(
        RadioInput,
        {
          label: translate("sell.labels.quantity_split_type"),
          checked: formData.quantity_split_type,
          onChange: (e) => {
            updateFormData("quantity_split_type", e.target.value);
            validateField(
              "quantity_split_type",
              e.target.value,
              formData
            );
          },
          datarequired: "true",
          options: ticketSplitTypeOptions,
          error: errors == null ? void 0 : errors.quantity_split_type
        }
      )
    ] }),
    /* @__PURE__ */ jsx("h2", { className: "text-lg font-bold mt-5 border-t pt-3", children: translate("sell.step_3_title") }),
    /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1", children: /* @__PURE__ */ jsxs("div", { className: "form-control mt-2", children: [
      /* @__PURE__ */ jsx("label", { className: "label text-sm font-medium pl-0", children: translate("sell.labels.restrictions") }),
      Object.keys(ticket.configurations.restrictions).map(
        (key) => /* @__PURE__ */ jsx(
          FilterCheckbox,
          {
            label: ticket.configurations.restrictions[key],
            value: key,
            checked: formData.restrictions.includes(
              Number(key)
            ),
            onChange: (value) => {
              updateFormData("restrictions", value);
            }
          },
          key
        )
      )
    ] }) }),
    /* @__PURE__ */ jsxs("label", { className: "flex items-center text-sm text-gray-700 cursor-pointer border-t pt-5", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "checkbox",
          className: "rounded border-gray-300 text-indigo-600 focus:ring-indigo-500",
          checked: formData.terms_agreed,
          onChange: (e) => updateFormData("terms_agreed", e.target.checked)
        }
      ),
      /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
        translate(
          "sell.read_agree_text",
          "I have read and agree to the"
        ),
        " ",
        /* @__PURE__ */ jsx("a", { href: "#", className: "link link-primary", children: translate(
          "sell.terms_conditions_text",
          "terms & conditions"
        ) })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "flex justify-end mt-4", children: /* @__PURE__ */ jsx("button", { className: "btn btn-primary", onClick: handleUpdateClick, children: translate("sell.update_btn", "Update") }) })
  ] });
};
function Edit() {
  const { ticketNo } = usePage().props;
  const { translate } = useTranslations();
  const { ticket, ticketLoading, getTicketDetail } = useEditTicket();
  useEffect(() => {
    getTicketDetail(ticketNo);
  }, []);
  if (ticketLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("sell.edit_head_title") }),
    /* @__PURE__ */ jsx("div", { className: "container px-4 py-3 mx-auto", children: ticket ? /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between border-b pb-3", children: [
        /* @__PURE__ */ jsxs("h2", { className: "text-md sm:text-xl font-bold", children: [
          translate("sell.edit_text"),
          ": #",
          ticket.ticket_no
        ] }),
        /* @__PURE__ */ jsx(
          Link,
          {
            className: "btn btn-sm btn-neutral btn-outline",
            href: route("my-account.tickets"),
            children: translate("sell.back_to_tickets_btn")
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("p", { className: "my-2", children: [
        /* @__PURE__ */ jsxs("b", { children: [
          translate("sell.labels.event"),
          ":"
        ] }),
        " ",
        ticket.event.name
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-y-2 my-2", children: [
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsxs("b", { children: [
            translate("sell.labels.sector_id"),
            ":"
          ] }),
          " ",
          ticket.sector.name
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsxs("b", { children: [
            translate("sell.labels.total_quantity"),
            ":"
          ] }),
          " ",
          ticket.quantity
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsxs("b", { children: [
            translate(
              "sell.labels.available_quantity"
            ),
            ":"
          ] }),
          " ",
          ticket.quantity - (ticket.reservations_sum_quantity ?? 0)
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsxs("b", { children: [
            translate("sell.labels.reserved_quantity"),
            ":"
          ] }),
          " ",
          ticket.reservations_sum_quantity ?? 0
        ] })
      ] }),
      /* @__PURE__ */ jsx(TicketForm, {})
    ] }) : /* @__PURE__ */ jsx("div", { className: "w-full bg-base-100 rounded-xl shadow p-5", children: /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center py-12", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsx(AlertTriangle, { className: "w-16 h-16 text-yellow-500 animate-pulse" }) }),
      /* @__PURE__ */ jsx("h1", { className: "text-3xl font-extrabold text-gray-800 mb-4", children: "Edit Ticket" }),
      /* @__PURE__ */ jsx("p", { className: "text-xl text-gray-600 mb-6", children: "Ticket not found, or you do not have permission to edit this ticket." })
    ] }) }) }) })
  ] });
}
Edit.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "tickets"
    }
  ) });
};
const __vite_glob_0_26 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Edit
}, Symbol.toStringTag, { value: "Module" }));
const fetchMyTickets = createAsyncThunk(
  "tickets/fetchMyTickets",
  async ({ url, filters, canAppendData = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendData, ...response.data };
    } else {
      throw new Error("Failed to fetch my tickets");
    }
  }
);
const initialState$4 = {
  tickets: [],
  isLoading: true,
  filterChanged: false,
  nextPageUrl: null,
  filters: {
    search: "",
    ticket_type: "",
    date_from: "",
    date_to: "",
    sort: ""
  }
};
const myTicketsSlice = createSlice({
  name: "myTickets",
  initialState: initialState$4,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$4.filters;
      state.filterChanged = true;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
      state.filterChanged = true;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchMyTickets.pending, (state) => {
      state.isLoading = true;
    }).addCase(fetchMyTickets.fulfilled, (state, action) => {
      const { tickets, meta } = action.payload;
      if (action.payload.canAppendData) {
        const existingTicketsMap = new Map(
          state.tickets.map((ticket) => [ticket.id, ticket])
        );
        const newTickets = tickets.filter(
          (ticket) => !existingTicketsMap.has(ticket.id)
        );
        state.tickets = [...state.tickets, ...newTickets];
      } else {
        state.tickets = tickets;
      }
      state.isLoading = false;
      state.filterChanged = false;
      state.nextPageUrl = meta.next_page_url;
    }).addCase(fetchMyTickets.rejected, (state) => {
      state.filterChanged = false;
      state.isLoading = false;
      state.tickets = [];
    });
  }
});
const { resetFilters: resetFilters$1, setFilter: setFilter$1 } = myTicketsSlice.actions;
const myTicketsReducer = myTicketsSlice.reducer;
function useMyTickets() {
  const dispatch = useDispatch();
  const { tickets, isLoading, filterChanged, nextPageUrl, filters } = useSelector((state) => state.myTickets);
  const getMyTickets = async () => {
    dispatch(
      fetchMyTickets({
        url: route("api.tickets.my-tickets"),
        filters
      })
    );
  };
  const clearFilters = () => {
    dispatch(resetFilters$1());
  };
  const updateFilter = (key, value) => {
    dispatch(setFilter$1({ key, value }));
  };
  const loadMoreTickets = () => {
    if (nextPageUrl) {
      dispatch(
        fetchMyTickets({
          url: nextPageUrl,
          filters,
          canAppendData: true
        })
      );
    }
  };
  return {
    tickets,
    isLoading,
    filterChanged,
    nextPageUrl,
    filters,
    getMyTickets,
    clearFilters,
    updateFilter,
    loadMoreTickets
  };
}
const TicketFilters = () => {
  const { translate } = useTranslations();
  const { filters, updateFilter, clearFilters, getMyTickets } = useMyTickets();
  const debouncedFilters = useDebounce(filters, 500);
  const isFirstRender = useRef(true);
  const today = /* @__PURE__ */ new Date();
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    getMyTickets();
  }, [debouncedFilters]);
  const ticketTypeOptions = [
    { label: translate("my_tickets.all_ticket_types"), value: "" },
    ...prepareOptionsFromEnum(translate("enums.ticket_types"))
  ];
  return /* @__PURE__ */ jsxs("div", { className: "border-b pb-5", children: [
    /* @__PURE__ */ jsxs("div", { className: "relative w-full mb-3", children: [
      /* @__PURE__ */ jsx(Search$1, { className: "absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" }),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          value: filters.search,
          onChange: (e) => updateFilter("search", e.target.value),
          placeholder: translate("my_tickets.search_placeholder"),
          className: "pl-10"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-3", children: [
      /* @__PURE__ */ jsx(
        DatePicker,
        {
          value: filters.date_from,
          className: "form-control mt-2",
          onChange: (value) => updateFilter("date_from", value),
          placeholder: translate("my_tickets.date_from_placeholder"),
          maxDate: filters.date_to || today
        }
      ),
      /* @__PURE__ */ jsx(
        DatePicker,
        {
          className: "form-control mt-2",
          value: filters.date_to,
          onChange: (value) => updateFilter("date_to", value),
          placeholder: translate("my_tickets.date_to_placeholder"),
          minDate: filters.date_from || void 0,
          maxDate: today
        }
      ),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          options: ticketTypeOptions,
          value: ticketTypeOptions.find(
            (opt) => opt.value === filters.ticket_type
          ),
          onChange: (option) => updateFilter("ticket_type", (option == null ? void 0 : option.value) || ""),
          placeholder: translate(
            "my_tickets.ticket_type_placeholder"
          )
        }
      ),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          options: translate("my_tickets.tickets_sort_options"),
          value: filters.sort ? translate("my_tickets.tickets_sort_options").find(
            (option) => option.value === filters.sort
          ) : null,
          onChange: (option) => updateFilter("sort", (option == null ? void 0 : option.value) || ""),
          placeholder: translate("my_tickets.sort_by_placeholder")
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "flex gap-6 justify-end mt-5", children: /* @__PURE__ */ jsx(
      "button",
      {
        onClick: clearFilters,
        className: "btn btn-outline btn-sm",
        children: translate("common.clear", "Clear")
      }
    ) })
  ] });
};
const TicketCard = ({ ticket, onDeleteClick }) => {
  var _a;
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx("div", { className: "card bg-base-100 shadow-md border border-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body p-5", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-start flex-wrap gap-2 border-b pb-3", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col min-[400px]:flex-row min-[400px]:items-center gap-2", children: [
        /* @__PURE__ */ jsxs("h2", { className: "text-base sm:text-lg font-semibold", children: [
          translate("my_tickets.ticket_text"),
          " #",
          ticket.ticket_no
        ] }),
        /* @__PURE__ */ jsx(
          "span",
          {
            className: `badge badge-outline capitalize w-fit badge-${ticket.ticket_type.color}`,
            children: ticket.ticket_type.label
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsx(
          Link,
          {
            className: "text-info",
            href: route(
              "my-account.tickets.edit",
              ticket.ticket_no
            ),
            children: /* @__PURE__ */ jsx(SquarePen, { className: "w-5 h-5" })
          }
        ),
        ticket.orders_count === 0 && ticket.reservations_count === 0 ? /* @__PURE__ */ jsx(
          "span",
          {
            className: "text-error cursor-pointer",
            onClick: () => onDeleteClick(ticket),
            children: /* @__PURE__ */ jsx(Trash2, { className: "w-5 h-5" })
          }
        ) : /* @__PURE__ */ jsx(
          "span",
          {
            className: "tooltip tooltip-left text-gray-400",
            "data-tip": translate(
              "my_tickets.delete_tooltip_text"
            ),
            children: /* @__PURE__ */ jsx(Trash2, { className: "w-5 h-5" })
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center sm:flex-row gap-4 mt-2", children: [
      /* @__PURE__ */ jsx("figure", { className: "relative h-20 bg-gray-200 object-cover rounded border border-base-300", children: ticket.event.image !== "" ? /* @__PURE__ */ jsx(
        "img",
        {
          src: ticket.event.image,
          alt: ((_a = ticket.event.image_alt) == null ? void 0 : _a.alt) || ticket.event.name,
          className: "w-24 h-full object-cover"
        }
      ) : /* @__PURE__ */ jsx(
        "img",
        {
          src: "/img/ticketgol-logo.png",
          alt: ticket.event.name,
          className: "w-24"
        }
      ) }),
      /* @__PURE__ */ jsxs("div", { className: "flex-1 space-y-2 text-sm text-base-content/70", children: [
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
            translate("sell.labels.event"),
            ":"
          ] }),
          " ",
          ticket.event.name
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2", children: [
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.total_quantity"),
              ":"
            ] }),
            " ",
            ticket.quantity
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.reserved_quantity"),
              ":"
            ] }),
            " ",
            ticket.reservations_sum_quantity ?? 0
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate(
                "sell.labels.available_quantity"
              ),
              ":"
            ] }),
            " ",
            ticket.quantity - (ticket.reservations_sum_quantity ?? 0)
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.sold_tickets"),
              ":"
            ] }),
            " ",
            ticket.sold_quantity ?? 0
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.price_text"),
              ":"
            ] }),
            " ",
            "€",
            ticket.price
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.event_date"),
              ":"
            ] }),
            " ",
            formatDate(ticket.event.date)
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("sell.labels.sector_id"),
              ":"
            ] }),
            " ",
            ticket.sector.name
          ] }),
          /* @__PURE__ */ jsxs("p", { children: [
            /* @__PURE__ */ jsxs("span", { className: "font-medium text-base-content", children: [
              translate("common.labels.created_at"),
              ":"
            ] }),
            " ",
            formatDate(ticket.created_at)
          ] })
        ] })
      ] })
    ] })
  ] }) });
};
function TicketDeleteModal({ open, onClose, ticket }) {
  const { translate } = useTranslations();
  const { getMyTickets } = useMyTickets();
  const isVisible = open && ticket;
  const [disabled, setDisabled] = useState(false);
  const handleDeleteClick = async () => {
    var _a, _b;
    setDisabled(true);
    try {
      const { data } = await axios.delete(
        route("api.tickets.delete", { ticketNo: ticket.ticket_no })
      );
      if (data.success) {
        setDisabled(false);
        onClose();
        getMyTickets();
        toast.success(data.message);
      }
    } catch (error) {
      setDisabled(false);
      onClose();
      if (((_a = error.response) == null ? void 0 : _a.status) === 400) {
        toast.error((_b = error.response.data) == null ? void 0 : _b.message);
      } else {
        toast.error(translate("common.something_wrong"));
      }
    }
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx("div", { className: `modal ${isVisible ? "modal-open" : ""}`, children: /* @__PURE__ */ jsx("div", { className: "modal-box max-w-sm", children: ticket && /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-sm btn-circle btn-ghost absolute right-2 top-2",
        onClick: onClose,
        children: "✕"
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-2", children: /* @__PURE__ */ jsx("div", { className: "bg-error/20 text-error rounded-full p-2", children: /* @__PURE__ */ jsx(Trash2, { className: "w-6 h-6" }) }) }),
    /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-center border-b-2 pb-3", children: translate("my_tickets.delete_ticket_text") }),
    /* @__PURE__ */ jsx("p", { className: "py-4 text-center", children: translate(
      "my_tickets.delete_ticket_confirm_text"
    ) }),
    /* @__PURE__ */ jsx("div", { className: "modal-action", children: /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-sm btn-error text-white",
        onClick: handleDeleteClick,
        disabled,
        children: translate("common.delete_btn")
      }
    ) })
  ] }) }) }) });
}
function Index() {
  const { translate } = useTranslations();
  const [selectedTicket, setSelectedTicket] = useState(null);
  const {
    tickets,
    isLoading,
    filterChanged,
    getMyTickets,
    nextPageUrl,
    loadMoreTickets
  } = useMyTickets();
  useEffect(() => {
    getMyTickets();
  }, []);
  const [observerRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreTickets,
    rootMargin: "100px"
  });
  const handleDeleteClick = (ticket) => {
    setSelectedTicket(ticket);
  };
  const closeDeleteModal = () => {
    setSelectedTicket(null);
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("my_tickets.head_title") }),
    /* @__PURE__ */ jsxs("div", { className: "container px-4 py-3 mx-auto", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center border-b pb-5 mb-6", children: [
        /* @__PURE__ */ jsx("h1", { className: "text-2xl font-bold", children: translate("my_tickets.page_title") }),
        /* @__PURE__ */ jsx(
          Link,
          {
            className: "btn btn-sm btn-primary",
            href: route("selltickets"),
            children: translate("my_tickets.sell_tickets_btn")
          }
        )
      ] }),
      /* @__PURE__ */ jsx(TicketFilters, {}),
      /* @__PURE__ */ jsx("div", { className: "space-y-4 max-h-[70vh] overflow-y-auto my-5", children: !isLoading && tickets.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("my_tickets.no_tickets") }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("my_tickets.no_tickets_details") })
      ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
        filterChanged ? /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          tickets.map((ticket) => /* @__PURE__ */ jsx(
            TicketCard,
            {
              ticket,
              onDeleteClick: handleDeleteClick
            },
            ticket.id
          )),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          )
        ] }),
        isLoading && !filterChanged && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
      ] }) })
    ] }),
    /* @__PURE__ */ jsx(
      TicketDeleteModal,
      {
        open: !!selectedTicket,
        onClose: closeDeleteModal,
        ticket: selectedTicket
      }
    )
  ] });
}
Index.layout = (page) => {
  return /* @__PURE__ */ jsx(AppLayout$1, { children: /* @__PURE__ */ jsx(
    MyAccountLayout,
    {
      children: page,
      auth: page.props.auth,
      activeMenu: "tickets"
    }
  ) });
};
const __vite_glob_0_27 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Index
}, Symbol.toStringTag, { value: "Module" }));
const RECENT_SEARCHES_KEY = "recent_searches";
const MAX_ENTRIES = 10;
function getRecentSearches() {
  if (typeof window === "undefined") return [];
  const data = localStorage.getItem(RECENT_SEARCHES_KEY);
  return data ? JSON.parse(data) : [];
}
function addRecentSearch(query) {
  if (!query) return;
  const items = getRecentSearches().filter((item) => item !== query);
  items.unshift(query);
  if (items.length > MAX_ENTRIES) items.pop();
  localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(items));
}
function clearRecentSearches() {
  localStorage.removeItem(RECENT_SEARCHES_KEY);
}
function Search() {
  const { props } = usePage();
  const { translate } = useTranslations();
  const [query, setQuery] = useState(props.q);
  const [searchTerm, setSearchTerm] = useState(props.q);
  const [results, setResults] = useState([]);
  const [page, setPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [loading, setLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showRecent, setShowRecent] = useState(false);
  const debouncedUpdate = useMemo(
    () => debounce((val) => {
      setSearchTerm(val);
      setPage(1);
    }, 500),
    []
  );
  const handleInputChange = (e) => {
    const val = e.target.value;
    const url = `/search?q=${encodeURIComponent(val)}`;
    router.visit(url, {
      preserveState: true,
      replace: true,
      only: []
      // no server-side props
    });
    setQuery(val);
    debouncedUpdate(val);
  };
  const fetchResults = useCallback(
    async (search, page2 = 1, append = false) => {
      if (!search) {
        setResults([]);
        setShowRecent(true);
        setHasNextPage(false);
        return;
      }
      setLoading(true);
      try {
        const response = await axios$1.get(route("api.search.index"), {
          params: { q: search, page: page2 }
        });
        const newResults = response.data.results.data;
        const { current_page, last_page } = response.data.results.pagination;
        setResults(
          (prev) => append ? [...prev, ...newResults] : newResults
        );
        setPage(current_page);
        setHasNextPage(current_page < last_page);
        addRecentSearch(search);
        setRecentSearches(getRecentSearches());
        setShowRecent(false);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    },
    []
  );
  useEffect(() => {
    fetchResults(searchTerm, 1, false);
  }, [searchTerm]);
  const [observeRef] = useInfiniteScroll({
    loading,
    hasNextPage,
    onLoadMore: () => fetchResults(searchTerm, page + 1, true),
    rootMargin: "100px"
  });
  useEffect(() => {
    setRecentSearches(getRecentSearches());
  }, []);
  const updateSearchQuery = useCallback((e) => {
    const term = e.target.innerText;
    const url = `/search?q=${encodeURIComponent(term)}`;
    router.visit(url, {
      preserveState: true,
      replace: true,
      only: []
    });
    setQuery(term);
    debouncedUpdate(term);
    setShowRecent(false);
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: "Search" }),
    /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto px-4 py-10", children: [
      /* @__PURE__ */ jsxs("h1", { className: "text-3xl font-bold mb-6 flex", children: [
        translate("common.search_results_for"),
        " ",
        searchTerm && /* @__PURE__ */ jsxs("span", { className: "text-gray-500 ml-2", children: [
          '"',
          searchTerm,
          '"'
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
        /* @__PURE__ */ jsxs("div", { className: "join w-full relative", children: [
          /* @__PURE__ */ jsx(Search$1, { className: "absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              type: "text",
              className: "pl-10 input input-bordered w-full text-gray-700",
              placeholder: translate("common.search_placeholder"),
              value: query,
              onChange: handleInputChange
            }
          )
        ] }),
        query.trim() === "" && showRecent && recentSearches.length > 0 && /* @__PURE__ */ jsxs("div", { className: "mt-2 bg-white border rounded shadow p-4", children: [
          /* @__PURE__ */ jsx("div", { className: "font-medium text-gray-600 mb-2", children: translate("common.recent_searches") }),
          /* @__PURE__ */ jsx("div", { className: "flex", children: recentSearches.map((term, i) => /* @__PURE__ */ jsx(
            "span",
            {
              className: "badge badge-outline badge-lg mr-2 cursor-pointer hover:bg-blue-100 text-blue-600",
              onClick: updateSearchQuery,
              children: term
            },
            i
          )) }),
          /* @__PURE__ */ jsx(
            "button",
            {
              className: "mt-2 text-sm text-gray-400 hover:text-red-500",
              onClick: () => {
                clearRecentSearches();
                setRecentSearches([]);
              },
              children: translate("common.clear_recent_searches")
            }
          )
        ] })
      ] }),
      !loading && searchTerm && results.length === 0 ? /* @__PURE__ */ jsx("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("common.no_search_results_found") }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx("ul", { className: "bg-white shadow rounded divide-y", children: results.map((result) => /* @__PURE__ */ jsx(
          SearchListResult,
          {
            result,
            query
          },
          `${result.type}-${result.id}`
        )) }),
        (hasNextPage || loading) && /* @__PURE__ */ jsx(
          "div",
          {
            ref: observeRef,
            className: "flex justify-center py-6",
            children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-md" })
          }
        )
      ] })
    ] })
  ] });
}
Search.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_28 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Search
}, Symbol.toStringTag, { value: "Module" }));
function StadiumCard({ stadium }) {
  return /* @__PURE__ */ jsxs("div", { className: "card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow", children: [
    /* @__PURE__ */ jsx("figure", { className: "relative h-48 bg-gray-200", children: stadium.image !== "" ? /* @__PURE__ */ jsx(
      "img",
      {
        src: stadium.image,
        alt: stadium.image_alt || stadium.name,
        className: "w-full h-full object-cover"
      }
    ) : /* @__PURE__ */ jsx("img", { src: "/img/ticketgol-logo.png", alt: stadium.name }) }),
    /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx(
        "div",
        {
          className: "tooltip tooltip-neutral w-fit text-left",
          "data-tip": stadium.name,
          tabIndex: 0,
          children: /* @__PURE__ */ jsx("h2", { className: "card-title line-clamp-1 max-w-xs", children: stadium.name })
        }
      ),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center text-gray-600", children: [
        /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
        /* @__PURE__ */ jsxs("span", { className: "text-sm truncate", children: [
          stadium.address_line_1,
          ", ",
          stadium.address_line_2,
          ",",
          " ",
          stadium.postcode
        ] })
      ] })
    ] })
  ] });
}
const fetchStadiums = createAsyncThunk(
  "stadiums/fetchStadiums",
  async ({ url, filters, canAppendStadiums = false }) => {
    const response = await axios$1.post(url, filters);
    if (response.data.success === true) {
      return { canAppendStadiums, ...response.data };
    } else {
      throw new Error("Failed to fetch stadiums");
    }
  }
);
const fetchFilterOptions = createAsyncThunk(
  "stadiums/fetchFilterOptions",
  async () => {
    const response = await axios$1.get(route("api.stadiums.filters"));
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error("Failed to fetch filter options");
    }
  }
);
const initialState$3 = {
  stadiums: [],
  loading: true,
  nextPageUrl: null,
  filterOptions: {},
  filterOptionsLoading: false,
  isFilterOptionsInitialized: false,
  filters: {
    countries: [],
    search: "",
    sort: ""
  }
};
const stadiumsSlice = createSlice({
  name: "stadiums",
  initialState: initialState$3,
  reducers: {
    resetFilters: (state) => {
      state.filters = initialState$3.filters;
    },
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    resetNextPageUrl: (state) => {
      state.nextPageUrl = null;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchStadiums.pending, (state) => {
      state.loading = true;
    }).addCase(fetchStadiums.fulfilled, (state, action) => {
      const { stadiums, meta } = action.payload;
      if (action.payload.canAppendStadiums) {
        const existingStadiumsMap = new Map(
          state.stadiums.map((stadium) => [stadium.id, stadium])
        );
        const newStadiums = stadiums.filter(
          (stadium) => !existingStadiumsMap.has(stadium.id)
        );
        state.stadiums = [...state.stadiums, ...newStadiums];
      } else {
        state.stadiums = stadiums;
      }
      state.nextPageUrl = meta.next_page_url;
      state.loading = false;
    }).addCase(fetchStadiums.rejected, (state) => {
      state.stadiums = [];
      state.loading = false;
    });
    builder.addCase(fetchFilterOptions.pending, (state) => {
      state.filterOptionsLoading = true;
    }).addCase(fetchFilterOptions.fulfilled, (state, action) => {
      state.filterOptions = action.payload;
      state.filterOptionsLoading = false;
      state.isFilterOptionsInitialized = true;
    }).addCase(fetchFilterOptions.rejected, (state) => {
      state.filterOptions = {};
      state.filterOptionsLoading = false;
    });
  }
});
const { resetFilters, setFilter, resetNextPageUrl } = stadiumsSlice.actions;
const stadiumsReducer = stadiumsSlice.reducer;
function useStadiums() {
  const {
    stadiums,
    loading,
    filters,
    filterOptions,
    nextPageUrl,
    isFilterOptionsInitialized
  } = useSelector((state) => state.stadiums);
  const dispatch = useDispatch();
  const updateFilter = (key, value) => {
    dispatch(setFilter({ key, value }));
  };
  const clearFilters = useCallback(() => {
    dispatch(resetFilters());
  }, []);
  const fetchOptions = () => {
    if (isFilterOptionsInitialized) {
      return;
    }
    dispatch(fetchFilterOptions());
  };
  const fetchStadiumsInitially = () => {
    if (isFilterOptionsInitialized > 0) {
      return;
    }
    dispatch(
      fetchStadiums({
        url: route("api.stadiums.index"),
        filters
      })
    );
  };
  const refreshStadiums = () => {
    dispatch(
      fetchStadiums({
        url: route("api.stadiums.index"),
        filters
      })
    );
  };
  const clearNextPageUrl = () => {
    dispatch(resetNextPageUrl());
  };
  const loadMoreStadiums = () => {
    if (!nextPageUrl) {
      return;
    }
    return dispatch(
      fetchStadiums({
        url: nextPageUrl,
        filters,
        canAppendStadiums: true
      })
    );
  };
  return {
    stadiums,
    loading,
    filterOptions,
    filters,
    updateFilter,
    clearFilters,
    fetchOptions,
    fetchStadiumsInitially,
    refreshStadiums,
    nextPageUrl,
    loadMoreStadiums,
    clearNextPageUrl
  };
}
function StadiumSidebar() {
  const { translate } = useTranslations();
  const {
    filters,
    clearFilters,
    updateFilter,
    filterOptions,
    refreshStadiums
  } = useStadiums();
  const debouncedFilters = useDebounce(filters, 800);
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    refreshStadiums();
  }, [debouncedFilters]);
  return /* @__PURE__ */ jsxs("aside", { className: "md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3", children: [
    /* @__PURE__ */ jsx("h2", { className: "font-semibold mt-8 mb-4", children: translate("stadiums.filters_title", "Filters") }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "text",
          placeholder: translate(
            "stadiums.search_placeholder",
            "Search stadiums..."
          ),
          className: "input input-bordered w-full",
          value: (filters == null ? void 0 : filters.search) || "",
          onChange: (e) => updateFilter("search", e.target.value)
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: clearFilters,
          className: "text-gray-500 hover:text-gray-800 transition-colors",
          title: translate("stadiums.reset_filters", "Reset Filters"),
          children: /* @__PURE__ */ jsx(XCircle, { size: 24 })
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsx(
      FilterSelectCollapse,
      {
        title: translate("stadiums.countries_title", "Countries"),
        placeholder: translate(
          "stadiums.countries_placeholder",
          "Select countries"
        ),
        options: filterOptions["countries"],
        selectedOption: filters["countries"],
        onChange: (value) => {
          updateFilter("countries", value);
        },
        isMulti: true
      },
      "countries"
    )
  ] });
}
function Stadiums() {
  const { translate } = useTranslations();
  const {
    stadiums,
    filters,
    updateFilter,
    fetchOptions,
    loading,
    loadMoreStadiums,
    clearNextPageUrl,
    nextPageUrl,
    fetchStadiumsInitially
  } = useStadiums();
  useEffect(() => {
    clearNextPageUrl();
    fetchOptions();
    const timer = setTimeout(() => {
      fetchStadiumsInitially();
    }, 10);
    return () => clearTimeout(timer);
  }, []);
  const [observerRef] = useInfiniteScroll({
    loading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreStadiums,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("stadiums.head_title", "Stadiums") }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8", children: [
      /* @__PURE__ */ jsx(StadiumSidebar, {}),
      /* @__PURE__ */ jsxs("main", { className: "md:w-3/4 w-full", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-8", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-2xl md:text-4xl font-bold", children: translate("stadiums.page_title", "All Stadiums") }),
          /* @__PURE__ */ jsx(
            SelectInput,
            {
              wrapperClass: "w-1/2 md:w-1/3",
              options: translate("stadiums.sort_options"),
              value: filters.sort ? translate("stadiums.sort_options").find(
                (option) => option.value === filters.sort
              ) : null,
              onChange: (selected) => updateFilter("sort", selected.value),
              placeholder: translate(
                "stadiums.sort_by_placeholder"
              )
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "h-[800px] overflow-y-auto my-5", children: !loading && stadiums.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate(
            "stadiums.no_stadiums",
            "Sorry, no stadiums match your filters."
          ) }),
          /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: translate("stadiums.no_stadiums_details") })
        ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: stadiums.map((stadium, index) => /* @__PURE__ */ jsx(
            Link,
            {
              href: route(
                "detail.show",
                stadium.slug
              ),
              className: "hover:scale-[1.02] transition-transform duration-150",
              children: /* @__PURE__ */ jsx(StadiumCard, { stadium })
            },
            `${stadium.id}-${index}`
          )) }),
          nextPageUrl && /* @__PURE__ */ jsx(
            "div",
            {
              ref: observerRef,
              className: "h-10"
            }
          ),
          loading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
        ] }) })
      ] })
    ] })
  ] });
}
Stadiums.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_29 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Stadiums
}, Symbol.toStringTag, { value: "Module" }));
const fetchStadiumDetail = createAsyncThunk(
  "stadiums/fetchStadiumDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.stadium;
    } else {
      throw new Error("Failed to fetch stadium detail");
    }
  }
);
const initialState$2 = {
  stadium: null,
  stadiumLoading: true
};
const stadiumDetailSlice = createSlice({
  name: "stadium",
  initialState: initialState$2,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchStadiumDetail.pending, (state) => {
      state.stadiumLoading = true;
    }).addCase(fetchStadiumDetail.fulfilled, (state, action) => {
      state.stadium = action.payload;
      state.stadiumLoading = false;
    }).addCase(fetchStadiumDetail.rejected, (state) => {
      state.stadium = null;
      state.stadiumLoading = false;
    });
  }
});
const stadiumDetailReducer = stadiumDetailSlice.reducer;
function useStadiumDetail() {
  const { stadium, stadiumLoading } = useSelector((state) => state.stadium);
  const dispatch = useDispatch();
  const getStadiumDetail = (slug) => {
    dispatch(
      fetchStadiumDetail({
        url: route("api.stadiums.show", { slug })
      })
    );
  };
  return {
    stadium,
    stadiumLoading,
    getStadiumDetail
  };
}
function StadiumEventList() {
  const { translate } = useTranslations();
  const { stadium } = useStadiumDetail();
  const {
    events,
    filters,
    updateFilter,
    eventLoading,
    loadMoreEvents,
    clearNextPageUrl,
    clearFilters,
    nextPageUrl,
    fetchEventsInitially
  } = useEvents();
  useEffect(() => {
    clearFilters();
    clearNextPageUrl();
    updateFilter("stadiums", [stadium.id]);
  }, [stadium.id]);
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchEventsInitially();
    }, 50);
    return () => clearTimeout(timer);
  }, [filters]);
  const [observerRef] = useInfiniteScroll({
    loading: eventLoading,
    hasNextPage: !!nextPageUrl,
    onLoadMore: loadMoreEvents,
    rootMargin: "100px"
  });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col md:flex-row md:justify-between md:items-center gap-2 mb-2 mt-8", children: [
      /* @__PURE__ */ jsxs("h1", { className: "text-base lg:text-2xl md:text-lg font-bold", children: [
        translate("stadiums.events_in_text"),
        " ",
        stadium.name
      ] }),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          wrapperClass: "w-full md:w-1/4",
          options: translate("stadiums.event_sort_options"),
          value: filters.sort ? translate("stadiums.event_sort_options").find(
            (option) => option.value === filters.sort
          ) : null,
          onChange: (selected) => updateFilter("sort", selected.value),
          placeholder: translate("stadiums.sort_by_placeholder")
        }
      )
    ] }),
    !eventLoading && events.length === 0 ? /* @__PURE__ */ jsx("div", { className: "flex flex-col items-center justify-center px-5 h-96 bg-base-100 mt-5 rounded-xl shadow-sm", children: /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("stadiums.no_events") }) }) : /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("div", { className: "container mx-auto py-5 grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4", children: events.map((event, index) => /* @__PURE__ */ jsx(
        "div",
        {
          className: "hover:scale-[1.02] cursor-pointer transition-transform duration-150",
          children: /* @__PURE__ */ jsx(
            EventCard,
            {
              event,
              translationFrom: translate("common.from"),
              translationBuyTickets: translate(
                "common.buy_tickets"
              )
            }
          )
        },
        event.id
      )) }),
      nextPageUrl && /* @__PURE__ */ jsx("div", { ref: observerRef, className: "h-10" }),
      eventLoading && /* @__PURE__ */ jsx("p", { className: "flex items-center justify-center h-32", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] })
  ] });
}
function Show() {
  const { slug } = usePage().props;
  const { stadium, getStadiumDetail, stadiumLoading } = useStadiumDetail();
  useEffect(() => {
    getStadiumDetail(slug);
  }, []);
  if (stadiumLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: stadium.name }),
      /* @__PURE__ */ jsx("meta", { name: "title", content: stadium.meta_title }),
      /* @__PURE__ */ jsx("meta", { name: "keywords", content: stadium.meta_keywords }),
      /* @__PURE__ */ jsx("meta", { name: "description", content: stadium.meta_description })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-10 py-5", children: [
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        stadium.image !== "" ? /* @__PURE__ */ jsx(
          "img",
          {
            src: stadium.image,
            alt: stadium.image_alt || stadium.name,
            className: "w-full h-96 object-cover rounded-xl"
          }
        ) : /* @__PURE__ */ jsx("div", { className: "bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500", children: /* @__PURE__ */ jsx(
          "img",
          {
            src: "/img/ticketgol-logo.png",
            alt: stadium.name,
            className: "object-cover w-1/3"
          }
        ) }),
        /* @__PURE__ */ jsxs("div", { className: "absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4", children: [
          /* @__PURE__ */ jsx("h1", { className: "text-xl sm:text-3xl font-bold mb-2", children: stadium.name }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-warning font-medium", children: [
            /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4 mr-2" }),
            /* @__PURE__ */ jsxs("span", { className: "text-base sm:text-lg", children: [
              stadium.address_line_1,
              ",",
              " ",
              stadium.address_line_2,
              ", ",
              stadium.country.name,
              ", ",
              stadium.postcode
            ] })
          ] }),
          /* @__PURE__ */ jsx("p", { className: "text-sm sm:text-base mt-2 max-w-xl", children: stadium.description })
        ] })
      ] }),
      /* @__PURE__ */ jsx(StadiumEventList, {})
    ] })
  ] });
}
Show.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_30 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Show
}, Symbol.toStringTag, { value: "Module" }));
dayjs.extend(utc);
function CheckoutTimer({ expiryDateTime, onExpire }) {
  const { translate } = useTranslations();
  const [timeLeft, setTimeLeft] = useState(getRemainingTime());
  const intervalRef = useRef(null);
  function getRemainingTime() {
    const now = dayjs();
    const expiry = dayjs.utc(expiryDateTime).local();
    const diffMs = expiry.diff(now);
    const expired = diffMs <= 0;
    const totalSeconds = Math.max(Math.floor(diffMs / 1e3), 0);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return { minutes, seconds, expired };
  }
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      const newTime = getRemainingTime();
      if (newTime.expired) {
        clearInterval(intervalRef.current);
        onExpire(true);
      }
      setTimeLeft(newTime);
    }, 1e3);
    return () => clearInterval(intervalRef.current);
  }, [expiryDateTime]);
  const padded = (num) => String(num).padStart(2, "0");
  return /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 text-sm font-semibold text-error", children: [
    /* @__PURE__ */ jsx("span", { children: translate(
      "common.complete_booking_in",
      "Complete your booking in"
    ) }),
    /* @__PURE__ */ jsxs("span", { className: "font-mono text-lg tracking-wider", children: [
      padded(timeLeft.minutes),
      ":",
      padded(timeLeft.seconds)
    ] }),
    /* @__PURE__ */ jsx("span", { children: translate("common.mins", "mins") })
  ] });
}
const fetchReservation = createAsyncThunk(
  "ticket/fetchReservation",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.reservation;
    } else {
      throw new Error("Failed to fetch reservation detail");
    }
  }
);
const initialState$1 = {
  reservation: null,
  reservationLoading: true,
  attendees: [],
  paymentState: {
    orderId: null,
    clientSecret: null,
    encryptedOrderId: null
  },
  showTimesUpPopup: false,
  step: 1,
  isAgreed: {
    terms: false,
    restrictions: false
  }
};
const ticketCheckoutSlice = createSlice({
  name: "ticketCheckout",
  initialState: initialState$1,
  reducers: {
    setAttendees: (state, action) => {
      const { index, field, value } = action.payload;
      state.attendees[index][field] = value;
    },
    setIsAgreed: (state, action) => {
      const { key, value } = action.payload;
      state.isAgreed[key] = value;
    },
    setShowTimesUpPopup: (state, action) => {
      const { value } = action.payload;
      state.showTimesUpPopup = value;
    },
    setStep: (state, action) => {
      const { value } = action.payload;
      state.step = value;
    },
    setPaymentState: (state, action) => {
      state.paymentState = action.payload;
    },
    resetReservation: (state) => {
      state.reservation = null;
      state.reservationLoading = true;
      state.attendees = [];
      state.paymentState = {
        orderId: null,
        clientSecret: null,
        encryptedOrderId: null
      };
      state.showTimesUpPopup = false;
      state.step = 1;
      state.isAgreed = {
        terms: false,
        restrictions: false
      };
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchReservation.pending, (state) => {
      state.reservation = null;
      state.reservationLoading = true;
    }).addCase(fetchReservation.fulfilled, (state, action) => {
      state.reservation = action.payload;
      if (state.reservation) {
        state.attendees = Array.from(
          { length: state.reservation.quantity },
          () => ({
            name: "",
            email: "",
            gender: "",
            dob: ""
          })
        );
        if (state.reservation.order) {
          state.paymentState = {
            orderId: state.reservation.order.id,
            clientSecret: state.reservation.order.clientSecret,
            encryptedOrderId: state.reservation.order.encryptedOrderId
          };
          state.attendees = state.reservation.order.attendees.map(
            (attendee) => ({
              name: attendee.name || "",
              email: attendee.email || "",
              gender: attendee.gender || "",
              dob: attendee.dob || ""
            })
          );
        }
      }
      state.reservationLoading = false;
    }).addCase(fetchReservation.rejected, (state) => {
      state.reservation = null;
      state.reservationLoading = false;
    });
  }
});
const {
  setAttendees,
  setIsAgreed,
  setShowTimesUpPopup,
  setStep: setStep$1,
  setPaymentState,
  resetReservation
} = ticketCheckoutSlice.actions;
const ticketCheckoutReducer = ticketCheckoutSlice.reducer;
function useTicketCheckout() {
  const {
    reservation,
    reservationLoading,
    attendees,
    showTimesUpPopup,
    step,
    paymentState,
    isAgreed
  } = useSelector((state) => state.ticketCheckout);
  const dispatch = useDispatch();
  const getReservation = (reservationId) => {
    dispatch(
      fetchReservation({
        url: route("api.reservations.detail", { reservationId })
      })
    );
  };
  const clearReservation = (value) => {
    dispatch(resetReservation());
  };
  const updateAttendees = (index, field, value) => {
    dispatch(setAttendees({ index, field, value }));
  };
  const updateIsAgreed = (key, value) => {
    dispatch(setIsAgreed({ key, value }));
  };
  const handleTimesUpPopup = (value) => {
    dispatch(setShowTimesUpPopup({ value }));
  };
  const nextStep = () => {
    dispatch(setStep$1({ value: Math.min(step + 1, 4) }));
  };
  const updatePaymentState = (values) => {
    dispatch(setPaymentState(values));
  };
  const prevStep = () => {
    dispatch(setStep$1({ value: Math.max(step - 1, 1) }));
  };
  const handleTimesUpPopupCloseClick = () => {
    router.visit(route("detail.show", reservation.ticket.event.slug));
  };
  return {
    reservation,
    reservationLoading,
    getReservation,
    clearReservation,
    attendees,
    isAgreed,
    showTimesUpPopup,
    updateAttendees,
    updateIsAgreed,
    handleTimesUpPopup,
    handleTimesUpPopupCloseClick,
    step,
    paymentState,
    updatePaymentState,
    nextStep,
    prevStep
  };
}
function TicketDetailSection() {
  const { translate } = useTranslations();
  const { reservation } = useTicketCheckout();
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { className: "px-5 mb-5", children: [
    /* @__PURE__ */ jsx("h2", { className: "mt-5 text-xl font-bold leading-tight", children: /* @__PURE__ */ jsx(
      Link,
      {
        href: route(
          "detail.show",
          reservation.ticket.event.slug
        ),
        className: "hover:underline",
        children: reservation.ticket.event.name
      }
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 font-bold text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx(Calendar, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(EventDateTime, { event: reservation.ticket.event })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 font-bold text-sm text-gray-700 hover:underline", children: [
      /* @__PURE__ */ jsx(MapPin, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx(
        Link,
        {
          href: route(
            "detail.show",
            reservation.ticket.event.stadium.slug
          ),
          children: /* @__PURE__ */ jsxs("span", { children: [
            reservation.ticket.event.stadium.name,
            ",",
            " ",
            reservation.ticket.event.stadium.address_line_1,
            ",",
            " ",
            reservation.ticket.event.stadium.address_line_2,
            ",",
            " ",
            reservation.ticket.event.stadium.country.name,
            ",",
            " ",
            reservation.ticket.event.stadium.postcode
          ] })
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 font-bold text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx(Blocks, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsx("p", { children: reservation.ticket.sector.name })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mt-3 flex items-center gap-2 font-bold text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx(Tickets, { className: "w-4 h-4" }),
      /* @__PURE__ */ jsxs("p", { children: [
        reservation.quantity,
        " ",
        translate("ticket.tickets_text")
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "mt-3 flex items-center gap-2 text-sm text-gray-700", children: /* @__PURE__ */ jsx(
      "p",
      {
        className: `badge badge-outline capitalize badge-${reservation.ticket.ticket_type.color}`,
        children: reservation.ticket.ticket_type.label
      }
    ) })
  ] }) });
}
function QuantityConfirmStep() {
  const { reservation, nextStep } = useTicketCheckout();
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h4", { className: "font-bold", children: translate(
      "ticket.confirm_text",
      "Please confirm the tickets you would like to purchase"
    ) }),
    /* @__PURE__ */ jsx("p", { className: "mt-2 font-medium", children: reservation.ticket.sector.name }),
    /* @__PURE__ */ jsxs("p", { className: "mt-2", children: [
      translate("ticket.total_tickets", "Total Tickets"),
      ":",
      " ",
      reservation.quantity
    ] }),
    /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-primary mt-4 float-right",
        onClick: nextStep,
        children: translate("ticket.continue_btn", "Continue")
      }
    )
  ] }) });
}
function ContactAddressStep() {
  var _a;
  const { translate } = useTranslations();
  const { reservation, nextStep, prevStep } = useTicketCheckout();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate(
      "ticket.contact_address_text",
      "Contact & Address Information"
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700 mb-2", children: [
      translate("common.labels.email", "Email"),
      " :",
      " ",
      /* @__PURE__ */ jsx("strong", { children: reservation.user.email })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700 mb-2", children: [
      translate("common.labels.phone", "Phone"),
      " :",
      " ",
      /* @__PURE__ */ jsx("strong", { children: reservation.user.user_detail.phone })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700 mb-2", children: [
      translate("common.labels.address", "Address"),
      " :",
      " ",
      /* @__PURE__ */ jsxs("strong", { children: [
        reservation.user.user_detail.address,
        ",",
        " ",
        reservation.user.user_detail.city,
        ",",
        " ",
        (_a = reservation.user.user_detail.country) == null ? void 0 : _a.name,
        reservation.user.user_detail.country && ", ",
        reservation.user.user_detail.zip
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("ticket.back_btn", "Back") }),
      /* @__PURE__ */ jsx("button", { className: "btn btn-primary", onClick: nextStep, children: translate("ticket.continue_btn", "Continue") })
    ] })
  ] });
}
function AttendeesStep() {
  const { translate } = useTranslations();
  const {
    attendees,
    updateAttendees,
    nextStep,
    prevStep,
    paymentState
  } = useTicketCheckout();
  const today = dayjs().format("YYYY-MM-DD");
  const validationSchema2 = {
    attendees: [
      "array",
      { rule: "minArrayLength", value: attendees.length }
    ],
    "attendees.*.name": ["required"],
    "attendees.*.email": ["required", "email"],
    "attendees.*.gender": ["required"],
    "attendees.*.dob": ["required", { rule: "maxDate", value: today }]
  };
  const { errors, validate, validateField } = useValidation(validationSchema2);
  const genderOptions = prepareOptionsFromEnum(translate("enums.genders"));
  const handleNext = (e) => {
    e.preventDefault();
    const isValid = validate({ attendees });
    if (isValid) {
      nextStep();
    }
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate(
      "ticket.attendees_step_text",
      "Attendees Information"
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "text-sm text-warning mb-4", children: [
      /* @__PURE__ */ jsxs("strong", { children: [
        translate("ticket.note_label", "Note"),
        " :"
      ] }),
      " ",
      translate(
        "ticket.attendees_locked_info",
        "Once you click Confirm & Pay in the final step, you won't be able to edit attendee details."
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "space-y-4", children: attendees.map((attendee, index) => /* @__PURE__ */ jsxs("div", { className: "border rounded-lg p-4 shadow", children: [
      /* @__PURE__ */ jsxs("h5", { className: "font-semibold mb-3", children: [
        translate("ticket.attendee_text", "Attendee"),
        " ",
        index + 1
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: `${index}_name`,
            value: attendee.name,
            placeholder: translate(
              "common.placeholder.name"
            ),
            disabled: !!paymentState.orderId,
            datarequired: "true",
            onChange: (e) => {
              updateAttendees(
                index,
                "name",
                e.target.value
              );
              validateField(
                `attendees.${index}.name`,
                e.target.value,
                { attendees }
              );
            },
            onBlur: (e) => validateField(
              `attendees.${index}.name`,
              e.target.value,
              { attendees }
            ),
            error: errors[`attendees.${index}.name`]
          }
        ),
        /* @__PURE__ */ jsx(
          TextInput,
          {
            id: `${index}_email`,
            type: "email",
            value: attendee.email,
            datarequired: "true",
            placeholder: translate(
              "common.placeholder.email"
            ),
            disabled: !!paymentState.orderId,
            onChange: (e) => {
              updateAttendees(
                index,
                "email",
                e.target.value
              );
              validateField(
                `attendees.${index}.email`,
                e.target.value,
                { attendees }
              );
            },
            onBlur: (e) => validateField(
              `attendees.${index}.email`,
              e.target.value,
              { attendees }
            ),
            error: errors[`attendees.${index}.email`]
          }
        ),
        /* @__PURE__ */ jsx(
          SelectInput,
          {
            id: `${index}_gender`,
            placeholder: translate(
              "common.placeholder.gender"
            ),
            isDisabled: !!paymentState.orderId,
            datarequired: "true",
            options: genderOptions,
            value: genderOptions.find(
              (option) => option.value === attendee.gender
            ),
            onChange: (selectedOption) => {
              updateAttendees(
                index,
                "gender",
                selectedOption.value
              );
              validateField(
                `attendees.${index}.gender`,
                selectedOption.value,
                { attendees }
              );
            },
            onBlur: () => validateField(
              `attendees.${index}.gender`,
              attendee.gender,
              { attendees }
            ),
            error: errors[`attendees.${index}.gender`]
          }
        ),
        /* @__PURE__ */ jsx(
          DatePicker,
          {
            id: `${index}_dob`,
            value: attendee.dob,
            placeholder: translate(
              "common.placeholder.dob"
            ),
            disabled: !!paymentState.orderId,
            className: "form-control mt-2",
            onChange: (value) => {
              updateAttendees(index, "dob", value);
              validateField(
                `attendees.${index}.dob`,
                value,
                { attendees }
              );
            },
            onBlur: () => validateField(
              `attendees.${index}.dob`,
              attendee.dob,
              { attendees }
            ),
            maxDate: today,
            error: errors[`attendees.${index}.dob`]
          }
        )
      ] })
    ] }, index)) }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("ticket.back_btn", "Back") }),
      /* @__PURE__ */ jsx("button", { className: "btn btn-primary", onClick: handleNext, children: translate("ticket.continue_btn", "Continue") })
    ] })
  ] });
}
function RestrictionsSection({
  reservation,
  isAgreed,
  updateIsAgreed
}) {
  const { translate } = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("div", { className: "mt-4", children: /* @__PURE__ */ jsxs(
      "div",
      {
        className: `collapse collapse-plus border border-base-300 rounded-box 
                            ${isOpen ? "collapse-open" : ""}`,
        onClick: () => setIsOpen((prev) => !prev),
        children: [
          /* @__PURE__ */ jsx("div", { className: "collapse-title font-semibold flex justify-between items-center cursor-pointer", children: /* @__PURE__ */ jsx("span", { children: translate(
            "ticket.event_ticket_term_text",
            "Event & Ticket Terms"
          ) }) }),
          /* @__PURE__ */ jsxs("div", { className: "collapse-content text-sm text-gray-700", children: [
            Object.keys(reservation.ticket.event.restrictions).length > 0 && /* @__PURE__ */ jsxs("div", { className: "mt-1", children: [
              /* @__PURE__ */ jsx("p", { className: "font-bold", children: translate(
                "ticket.event_term_text",
                "Event Terms"
              ) }),
              /* @__PURE__ */ jsx("ul", { className: "list-disc ml-5", children: Object.entries(
                reservation.ticket.event.restrictions
              ).map(([id, name]) => /* @__PURE__ */ jsx("li", { children: name }, id)) })
            ] }),
            Object.keys(reservation.ticket.restrictions).length > 0 && /* @__PURE__ */ jsxs("div", { className: "mt-2", children: [
              /* @__PURE__ */ jsx("p", { className: "font-bold", children: translate(
                "ticket.ticket_term_text",
                "Ticket Terms"
              ) }),
              /* @__PURE__ */ jsx("ul", { className: "list-disc ml-5", children: Object.entries(
                reservation.ticket.restrictions
              ).map(([id, name]) => /* @__PURE__ */ jsx("li", { children: name }, id)) })
            ] })
          ] })
        ]
      }
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "flex items-start text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx("label", { children: /* @__PURE__ */ jsx(
        "input",
        {
          type: "checkbox",
          className: "checkbox checkbox-sm",
          checked: isAgreed.restrictions,
          onChange: (e) => updateIsAgreed("restrictions", e.target.checked)
        }
      ) }),
      /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
        translate(
          "ticket.read_agree_text",
          "I have read and agree to the"
        ),
        " ",
        translate(
          "ticket.event_ticket_term_text",
          "Event & Ticket Terms"
        )
      ] })
    ] })
  ] });
}
function useStripePayment({ reservationId }) {
  const { translate } = useTranslations();
  const { reservation, attendees, paymentState, updatePaymentState } = useTicketCheckout();
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [cardCompleted, setCardCompleted] = useState(false);
  const createOrderIfNeeded = async () => {
    if (paymentState.orderId && paymentState.clientSecret) {
      return paymentState;
    }
    const params = {
      ticket_reservation_id: reservationId,
      event_id: reservation.ticket.event_id,
      currency_code: reservation.ticket.currency_code,
      attendees
    };
    const { data } = await axios$1.post(route("api.orders.store"), params);
    if (!data.success) {
      throw new Error("Order creation failed");
    }
    updatePaymentState({
      orderId: data.order_id,
      clientSecret: data.clientSecret,
      encryptedOrderId: data.encryptedOrderId
    });
    return {
      orderId: data.order_id,
      clientSecret: data.clientSecret,
      encryptedOrderId: data.encryptedOrderId
    };
  };
  const confirmPayment = async () => {
    var _a, _b, _c, _d;
    if (!stripe || !elements) {
      toast.error(translate("ticket.payment_element_error"));
      return;
    }
    if (!cardCompleted) {
      toast.error(
        translate(
          "ticket.card_detail_error",
          "Please enter your card details."
        )
      );
      return;
    }
    const cardElement = elements.getElement(CardElement);
    setLoading(true);
    try {
      const { orderId, clientSecret, encryptedOrderId } = await createOrderIfNeeded();
      const response = await axios$1.post(
        route("api.orders.check-status"),
        { order_id: encryptedOrderId }
      );
      if (response.data.is_completed) {
        toast.error(
          translate(
            "ticket.reservation_completed",
            "This ticket reservation has already been paid in another tab."
          )
        );
        setLoading(false);
        return;
      }
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: { card: cardElement }
      });
      if (result.error) {
        toast.error(result.error.message);
        return { success: false, error: result.error };
      }
      if (result.paymentIntent.status === "succeeded") {
        return {
          success: true,
          encryptedOrderId,
          orderId
        };
      }
      return { success: false, status: result.paymentIntent.status };
    } catch (error) {
      if (((_a = error.response) == null ? void 0 : _a.status) === 422 && ((_b = error.response.data) == null ? void 0 : _b.errors)) {
        toast.error((_d = Object.values((_c = error.response.data) == null ? void 0 : _c.errors)[0]) == null ? void 0 : _d[0]);
      } else {
        toast.error(translate("common.something_wrong"));
      }
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };
  return {
    loading,
    CardElement,
    confirmPayment,
    cardCompleted,
    setCardCompleted
  };
}
function ReviewConfirmStep() {
  const { translate } = useTranslations();
  const { reservationId } = usePage().props;
  const {
    reservation,
    isAgreed,
    updateIsAgreed,
    prevStep,
    showTimesUpPopup
  } = useTicketCheckout();
  const {
    loading,
    CardElement: CardElement2,
    confirmPayment,
    cardCompleted,
    setCardCompleted
  } = useStripePayment({
    reservationId
  });
  const handleConfirmClick = async (e) => {
    e.preventDefault();
    if (showTimesUpPopup) {
      toast.error(
        translate(
          "ticket.rerservation_session_expired",
          "Your ticket purchase session has expired."
        )
      );
      return;
    }
    if (!isAgreed.terms) {
      toast.error(
        translate(
          "ticket.conditions_terms",
          "Please read and agree to our terms and conditions"
        )
      );
      return;
    }
    if ((Object.keys(reservation.ticket.restrictions).length > 0 || Object.keys(reservation.ticket.event.restrictions).length > 0) && !isAgreed.restrictions) {
      toast.error(
        translate(
          "ticket.restrictions_terms",
          "Please read and agree to our Event & Ticket terms"
        )
      );
      return;
    }
    const result = await confirmPayment();
    if (result.success) {
      return router.visit(
        route("checkout.success", {
          orderId: result.encryptedOrderId,
          payment: "success"
        })
      );
    }
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx("div", { className: "relative", children: /* @__PURE__ */ jsxs("form", { className: "space-y-4", autoComplete: "off", children: [
    loading && /* @__PURE__ */ jsx("div", { className: "absolute inset-0 z-10 bg-white bg-opacity-70 flex items-center justify-center", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) }),
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate(
      "ticket.review_step_text",
      "Review & Confirm"
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
      translate("ticket.total_tickets", "Total Tickets"),
      /* @__PURE__ */ jsxs("span", { className: "float-right", children: [
        reservation.quantity,
        " x €",
        reservation.price
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsxs("div", { className: "bg-base-200 rounded-lg p-4 text-sm text-gray-800", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-1", children: [
        /* @__PURE__ */ jsx("span", { children: translate("ticket.subtotal_text", "Sub Total") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          reservation.subTotal
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-1", children: [
        /* @__PURE__ */ jsx("span", { children: translate(
          "ticket.service_charge_text",
          "Service Charge"
        ) }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          reservation.serviceCharge
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-2", children: [
        /* @__PURE__ */ jsx("span", { children: translate("ticket.tax_text", "Tax") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          reservation.taxRate
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between font-semibold text-base mt-2", children: [
        /* @__PURE__ */ jsx("span", { children: translate(
          "ticket.grand_total_text",
          "Grand Total"
        ) }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          reservation.grandTotal
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    /* @__PURE__ */ jsx("h2", { className: "text-lg font-bold mb-2", children: "Payment Details" }),
    /* @__PURE__ */ jsx("div", { className: "w-full border border-gray-300 rounded-lg px-4 py-3 bg-white focus-within:ring-2 focus-within:ring-primary focus-within:border-primary", children: /* @__PURE__ */ jsx(
      CardElement2,
      {
        options: {
          style: {
            base: {
              fontSize: "16px",
              color: "#000",
              fontFamily: "'Inter', sans-serif",
              "::placeholder": {
                color: "#a3a3a3"
              }
            },
            invalid: {
              color: "#f43f5e"
            }
          },
          hidePostalCode: true
        },
        onChange: (event) => {
          setCardCompleted(event.complete);
        }
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
    (Object.keys(reservation.ticket.restrictions).length > 0 || Object.keys(reservation.ticket.event.restrictions).length > 0) && /* @__PURE__ */ jsx(
      RestrictionsSection,
      {
        reservation,
        isAgreed,
        updateIsAgreed
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "flex items-start text-sm text-gray-700", children: [
      /* @__PURE__ */ jsx("label", { children: /* @__PURE__ */ jsx(
        "input",
        {
          type: "checkbox",
          className: "checkbox checkbox-sm",
          checked: isAgreed.terms,
          onChange: (e) => updateIsAgreed("terms", e.target.checked)
        }
      ) }),
      /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
        translate(
          "ticket.read_agree_text",
          "I have read and agree to the"
        ),
        " ",
        /* @__PURE__ */ jsx("a", { href: "#", className: "link link-primary", children: translate(
          "ticket.terms_conditions_text",
          "terms & conditions"
        ) })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("ticket.back_btn", "Back") }),
      /* @__PURE__ */ jsx(
        "button",
        {
          className: "btn btn-primary",
          onClick: handleConfirmClick,
          disabled: loading || !cardCompleted,
          children: translate(
            "ticket.confirm_pay_btn",
            "Confirm & Pay"
          )
        }
      )
    ] })
  ] }) }) });
}
function CheckoutStepsSection() {
  const { step } = useTicketCheckout();
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("ul", { className: "steps mb-5 w-full", children: [
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 1 ? "step-primary" : ""}`, children: translate("ticket.step_1_text", "Confirm Quantity") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 2 ? "step-primary" : ""}`, children: translate("ticket.step_2_text", "Contact & Address") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 3 ? "step-primary" : ""}`, children: translate("ticket.step_3_text", "Attendees") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 4 ? "step-primary" : ""}`, children: translate("ticket.step_4_text", "Review") })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider mt-0 mb-2" }),
    step === 1 && /* @__PURE__ */ jsx(QuantityConfirmStep, {}),
    step === 2 && /* @__PURE__ */ jsx(ContactAddressStep, {}),
    step === 3 && /* @__PURE__ */ jsx(AttendeesStep, {}),
    step === 4 && /* @__PURE__ */ jsx(ReviewConfirmStep, {})
  ] });
}
function CheckoutSessionExpired() {
  const { translate } = useTranslations();
  const { reservation } = useTicketCheckout();
  return /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center py-12", children: /* @__PURE__ */ jsxs("div", { className: "text-center py-16 px-6 bg-white shadow-xl rounded-2xl max-w-lg", children: [
    /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsx(Frown, { className: "w-16 h-16 text-primary animate-bounce" }) }),
    /* @__PURE__ */ jsx("h4", { className: "text-2xl font-extrabold text-gray-800 mb-4", children: translate("ticket.session_expired", "Session expired") }),
    /* @__PURE__ */ jsx("p", { className: "text-xl text-gray-600 mb-6", children: translate(
      "ticket.session_expired_desc",
      "Your ticket purchase session has timed out. Kindly use the link below to initiate a new order."
    ) }),
    reservation ? /* @__PURE__ */ jsx(
      Link,
      {
        href: route(
          "detail.show",
          reservation.ticket.event.slug
        ),
        className: "inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200",
        children: translate("ticket.event_back_btn", "Go Back to Event")
      },
      `${reservation.ticket.event.id}`
    ) : /* @__PURE__ */ jsx(
      Link,
      {
        href: route("events"),
        className: "inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200",
        children: translate("ticket.event_back_btn", "Go Back to Event")
      },
      "events"
    )
  ] }) });
}
function TimesUpPopup({ open, onClose }) {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsx("div", { className: `modal ${open ? "modal-open" : ""}`, children: /* @__PURE__ */ jsxs("div", { className: "modal-box max-w-sm", children: [
    /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold mb-4 text-center", children: translate("ticket.times_up_text", "Time's Up!") }),
    /* @__PURE__ */ jsx("div", { className: "divider" }),
    /* @__PURE__ */ jsx("p", { className: "mb-4 text-center", children: translate(
      "ticket.times_up_desc",
      "Click the button below to go back to the ticket selection page for this event."
    ) }),
    /* @__PURE__ */ jsx("div", { className: "modal-action flex justify-center", children: /* @__PURE__ */ jsx(
      "button",
      {
        onClick: onClose,
        className: "btn btn-primary btn-sm",
        children: translate("ticket.event_back_btn", "Go Back to Event")
      }
    ) })
  ] }) });
}
dayjs.extend(utc);
const stripePromise = loadStripe("pk_test_51R9mUfRhkfMMoe7tbpxmqFelFQxbuFopED7SEQO2D2wHqgWLWdhTfzumWOvE0pafB9BGt8OGuOABb8nAZwNQ964o00YeR7cowe");
function Checkout() {
  const { reservationId } = usePage().props;
  const { translate } = useTranslations();
  const {
    reservation,
    reservationLoading,
    getReservation,
    clearReservation,
    showTimesUpPopup,
    handleTimesUpPopup,
    handleTimesUpPopupCloseClick
  } = useTicketCheckout();
  useEffect(() => {
    clearReservation();
    getReservation(reservationId);
  }, []);
  if (reservationLoading) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Head, { title: "Loading..." }),
      /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-2xl" }) })
    ] });
  }
  const isValidReservation = reservation && (reservation.status === "active" || reservation.status === "processing");
  if (!isValidReservation) {
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(
        Head,
        {
          title: translate(
            "ticket.session_expired_title",
            "Checkout Session Expired"
          )
        }
      ),
      /* @__PURE__ */ jsx(CheckoutSessionExpired, {})
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("ticket.head_title", "Purchase Ticket") }),
    /* @__PURE__ */ jsxs(Elements, { stripe: stripePromise, children: [
      /* @__PURE__ */ jsxs("div", { className: "container mx-auto", children: [
        /* @__PURE__ */ jsx("div", { className: "sticky top-0 bg-base-100 py-2 justify-center flex z-10", children: dayjs.utc(reservation.expires_at).local().isAfter(dayjs()) && /* @__PURE__ */ jsx(
          CheckoutTimer,
          {
            expiryDateTime: reservation.expires_at,
            onExpire: handleTimesUpPopup
          }
        ) }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:flex-row gap-8 items-start px-6 py-5", children: [
          /* @__PURE__ */ jsx("div", { className: "w-full md:w-1/2 bg-base-100 rounded-xl shadow", children: /* @__PURE__ */ jsx(TicketDetailSection, {}) }),
          /* @__PURE__ */ jsx("div", { className: "w-full md:w-1/2 bg-base-100 rounded-xl shadow p-5", children: /* @__PURE__ */ jsx(CheckoutStepsSection, {}) })
        ] })
      ] }),
      /* @__PURE__ */ jsx(
        TimesUpPopup,
        {
          open: showTimesUpPopup,
          onClose: handleTimesUpPopupCloseClick
        }
      )
    ] })
  ] });
}
Checkout.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_31 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Checkout
}, Symbol.toStringTag, { value: "Module" }));
const fetchEventDetail = createAsyncThunk(
  "ticket/fetchEventDetail",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data.event;
    } else {
      throw new Error("Failed to fetch event detail");
    }
  }
);
const fetchTicketConfigurations = createAsyncThunk(
  "ticket/fetchTicketConfigurations",
  async ({ url }) => {
    const response = await axios$1.get(url);
    if (response.data.success === true) {
      return response.data;
    } else {
      throw new Error("Failed to fetch ticket configurations");
    }
  }
);
const initialState = {
  event: null,
  eventLoading: true,
  configurations: [],
  configurationsLoading: true,
  step: 1,
  formData: {
    event_id: "",
    quantity: 1,
    price: "",
    face_value_price: "",
    currency_code: "EUR",
    sector_id: "",
    sector_name: "",
    ticket_rows: "",
    ticket_seats: "",
    quantity_split_type: "any",
    ticket_type: "",
    description: "",
    restrictions: [],
    terms_agreed: false
  }
};
const ticketSellSlice = createSlice({
  name: "ticketSell",
  initialState,
  reducers: {
    setStep: (state, action) => {
      const { value } = action.payload;
      state.step = value;
    },
    setFormData: (state, action) => {
      const { key, value } = action.payload;
      if (key === "restrictions") {
        if (state.formData.restrictions.includes(value)) {
          state.formData.restrictions = state.formData.restrictions.filter(
            (item) => item !== value
          );
        } else {
          state.formData.restrictions.push(value);
        }
      } else {
        state.formData[key] = value;
      }
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchEventDetail.pending, (state) => {
      state.eventLoading = true;
    }).addCase(fetchEventDetail.fulfilled, (state, action) => {
      state.event = action.payload;
      state.formData.event_id = state.event.id;
      state.eventLoading = false;
    }).addCase(fetchEventDetail.rejected, (state) => {
      state.event = null;
      state.eventLoading = false;
    });
    builder.addCase(fetchTicketConfigurations.pending, (state) => {
      state.configurationsLoading = true;
    }).addCase(fetchTicketConfigurations.fulfilled, (state, action) => {
      state.configurations = action.payload;
      state.configurationsLoading = false;
    }).addCase(fetchTicketConfigurations.rejected, (state) => {
      state.configurations = [];
      state.configurationsLoading = false;
    });
  }
});
const { setStep, setFormData } = ticketSellSlice.actions;
const ticketSellReducer = ticketSellSlice.reducer;
function useTicketSell() {
  const { event, eventLoading, configurations, formData, step } = useSelector(
    (state) => state.ticketSell
  );
  const dispatch = useDispatch();
  const getEventDetail = (slug) => {
    dispatch(
      fetchEventDetail({
        url: route("api.events.show", { slug })
      })
    );
  };
  const getConfigurationsData = () => {
    dispatch(
      fetchTicketConfigurations({
        url: route("api.tickets.configurations")
      })
    );
  };
  const updateFormData = (key, value) => {
    dispatch(setFormData({ key, value }));
  };
  const nextStep = () => {
    dispatch(setStep({ value: Math.min(step + 1, 4) }));
  };
  const prevStep = () => {
    dispatch(setStep({ value: Math.max(step - 1, 1) }));
  };
  return {
    event,
    eventLoading,
    getEventDetail,
    formData,
    updateFormData,
    step,
    nextStep,
    prevStep,
    configurations,
    getConfigurationsData
  };
}
function QuantityPriceStep() {
  const { translate } = useTranslations();
  const { event, nextStep, formData, configurations, updateFormData } = useTicketSell();
  const validationSchema2 = {
    quantity: [
      "required",
      "number",
      { rule: "min", value: 1 },
      { rule: "max", value: configurations.max_quantity_per_ticket }
    ],
    price: [
      "required",
      "number",
      { rule: "min", value: 1 },
      { rule: "max", value: configurations.max_price_limit }
    ],
    face_value_price: [
      "required",
      "number",
      { rule: "min", value: 1 },
      { rule: "max", value: configurations.max_price_limit }
    ],
    sector_id: ["required"],
    ticket_rows: [{ rule: "maxlength", value: 100 }],
    ticket_seats: [{ rule: "maxlength", value: 200 }]
  };
  const { errors, validate, validateField } = useValidation(validationSchema2);
  const sectorOptions = prepareOptionsFromEnum(event.stadium_sectors);
  const subTotal = formData.quantity && formData.price ? formData.quantity * formData.price : 0;
  const serviceCharge = Math.round(
    formData.quantity * formData.price * configurations.service_charge_rate * 100
  ) / 100;
  const tax = Math.round(serviceCharge * configurations.tax_rate * 100) / 100;
  const grandTotal = subTotal + serviceCharge + tax;
  const handleContinue = (e) => {
    e.preventDefault();
    const isValid = validate(formData);
    if (!isValid) return;
    nextStep();
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate("sell.step_1_title") }),
    /* @__PURE__ */ jsx("form", { className: "space-y-4", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "quantity",
          type: "number",
          value: formData.quantity,
          min: "1",
          max: configurations.max_quantity_per_ticket,
          label: translate("sell.labels.quantity"),
          placeholder: translate("sell.placeholder.quantity"),
          datarequired: "true",
          onChange: (e) => updateFormData("quantity", e.target.value),
          error: errors == null ? void 0 : errors.quantity
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "price",
          type: "number",
          value: formData.price,
          label: translate("sell.labels.price"),
          min: "0",
          max: configurations.max_price_limit,
          step: "0.01",
          placeholder: translate("sell.placeholder.price"),
          datarequired: "true",
          onChange: (e) => updateFormData("price", e.target.value),
          onBlur: (e) => {
            updateFormData(
              "price",
              parseFloat(e.target.value).toFixed(2)
            );
            validateField(
              "price",
              formData.price,
              formData
            );
          },
          error: errors == null ? void 0 : errors.price
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "face_value_price",
          type: "number",
          value: formData.face_value_price,
          label: translate("sell.labels.face_value_price"),
          min: "0",
          max: configurations.max_price_limit,
          step: "0.01",
          placeholder: translate(
            "sell.placeholder.face_value_price"
          ),
          datarequired: "true",
          onChange: (e) => updateFormData(
            "face_value_price",
            e.target.value
          ),
          onBlur: (e) => {
            updateFormData(
              "face_value_price",
              parseFloat(e.target.value).toFixed(2)
            );
            validateField(
              "face_value_price",
              formData.face_value_price,
              formData
            );
          },
          error: errors == null ? void 0 : errors.face_value_price
        }
      ),
      /* @__PURE__ */ jsx(
        SelectInput,
        {
          id: "sector",
          label: translate("sell.labels.sector_id"),
          placeholder: translate(
            "sell.placeholder.sector_id"
          ),
          options: sectorOptions,
          datarequired: "true",
          value: sectorOptions.find(
            (opt) => opt.value === formData.sector_id
          ),
          onChange: (option) => {
            updateFormData(
              "sector_id",
              (option == null ? void 0 : option.value) || ""
            );
            updateFormData(
              "sector_name",
              (option == null ? void 0 : option.label) || ""
            );
            validateField(
              "sector_id",
              option == null ? void 0 : option.value,
              formData
            );
          },
          error: errors == null ? void 0 : errors.sector_id
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "ticket_rows",
          value: formData.ticket_rows,
          label: translate("sell.labels.ticket_rows"),
          placeholder: translate(
            "sell.placeholder.ticket_rows"
          ),
          onChange: (e) => updateFormData("ticket_rows", e.target.value),
          onBlur: (e) => validateField(
            "ticket_rows",
            formData.ticket_rows,
            formData
          ),
          error: errors == null ? void 0 : errors.ticket_rows
        }
      ),
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "ticket_seats",
          value: formData.ticket_seats,
          label: translate("sell.labels.ticket_seats"),
          placeholder: translate(
            "sell.placeholder.ticket_seats"
          ),
          onChange: (e) => updateFormData("ticket_seats", e.target.value),
          onBlur: (e) => validateField(
            "ticket_seats",
            formData.ticket_seats,
            formData
          ),
          error: errors == null ? void 0 : errors.ticket_seats
        }
      )
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "mt-5 w-full", children: /* @__PURE__ */ jsxs("div", { className: "bg-base-200 rounded-lg p-4 text-sm text-gray-800", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-1", children: [
        /* @__PURE__ */ jsx("span", { children: translate("sell.your_earning_text") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          subTotal.toFixed(2)
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-1", children: [
        /* @__PURE__ */ jsx("span", { children: translate("sell.service_charge_text") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          serviceCharge.toFixed(2)
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between mb-2", children: [
        /* @__PURE__ */ jsx("span", { children: translate("sell.tax_text") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          tax.toFixed(2)
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "divider m-0" }),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-between font-semibold text-base mt-2", children: [
        /* @__PURE__ */ jsx("span", { children: translate("sell.final_price_text") }),
        /* @__PURE__ */ jsxs("span", { children: [
          "€",
          grandTotal.toFixed(2)
        ] })
      ] })
    ] }) }),
    /* @__PURE__ */ jsx(
      "button",
      {
        className: "btn btn-primary mt-4 float-right",
        onClick: handleContinue,
        children: translate("sell.continue_btn")
      }
    )
  ] }) });
}
const validationSchema = {
  ticket_type: ["required"],
  quantity_split_type: ["required"],
  description: ["required", { rule: "maxlength", value: 255 }]
};
function TypeDetailStep() {
  const { translate } = useTranslations();
  const { prevStep, formData, updateFormData, nextStep } = useTicketSell();
  const { errors, validate, validateField } = useValidation(validationSchema);
  const ticketTypeOptions = prepareOptionsFromEnum(
    translate("enums.ticket_types")
  );
  const ticketSplitTypeOptions = prepareOptionsFromEnum(
    translate("enums.ticket_quantity_split_types")
  );
  const handleContinue = (e) => {
    e.preventDefault();
    const isValid = validate(formData);
    if (!isValid) return;
    nextStep();
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate("sell.step_2_text") }),
    /* @__PURE__ */ jsxs("form", { className: "space-y-4", children: [
      /* @__PURE__ */ jsx(
        TextInput,
        {
          id: "description",
          type: "textarea",
          value: formData.description,
          label: translate("sell.labels.description"),
          placeholder: translate("sell.placeholder.description"),
          datarequired: "true",
          onChange: (e) => updateFormData("description", e.target.value),
          onBlur: (e) => validateField(
            "description",
            formData.description,
            formData
          ),
          error: errors == null ? void 0 : errors.description
        }
      ),
      /* @__PURE__ */ jsx(
        RadioInput,
        {
          label: translate("sell.labels.ticket_type"),
          checked: formData.ticket_type,
          onChange: (e) => {
            updateFormData("ticket_type", e.target.value);
            validateField(
              "ticket_type",
              e.target.value,
              formData
            );
          },
          datarequired: "true",
          options: ticketTypeOptions,
          error: errors == null ? void 0 : errors.ticket_type
        }
      ),
      /* @__PURE__ */ jsx(
        RadioInput,
        {
          label: translate("sell.labels.quantity_split_type"),
          checked: formData.quantity_split_type,
          onChange: (e) => {
            updateFormData(
              "quantity_split_type",
              e.target.value
            );
            validateField(
              "quantity_split_type",
              e.target.value,
              formData
            );
          },
          datarequired: "true",
          options: ticketSplitTypeOptions,
          error: errors == null ? void 0 : errors.quantity_split_type
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("sell.back_btn", "Back") }),
      /* @__PURE__ */ jsx(
        "button",
        {
          className: "btn btn-primary",
          onClick: handleContinue,
          children: translate("sell.continue_btn", "Continue")
        }
      )
    ] })
  ] }) });
}
function RestrictionsStep() {
  const { translate } = useTranslations();
  const { prevStep, nextStep, configurations, formData, updateFormData } = useTicketSell();
  const restrictions = configurations.restrictions;
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate("sell.step_3_title") }),
    /* @__PURE__ */ jsx("form", { className: "space-y-4", children: /* @__PURE__ */ jsxs("div", { className: "form-control mt-2", children: [
      /* @__PURE__ */ jsx("label", { className: "label text-sm font-medium pl-0", children: translate("sell.labels.restrictions") }),
      Object.keys(restrictions).map((key) => /* @__PURE__ */ jsx(
        FilterCheckbox,
        {
          label: restrictions[key],
          value: key,
          checked: formData.restrictions.includes(key),
          onChange: (value) => {
            updateFormData("restrictions", value);
          }
        },
        key
      ))
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("sell.back_btn", "Back") }),
      /* @__PURE__ */ jsx("button", { className: "btn btn-primary", onClick: nextStep, children: translate("sell.continue_btn") })
    ] })
  ] }) });
}
function ReviewStep() {
  const { translate } = useTranslations();
  const { prevStep, formData, updateFormData } = useTicketSell();
  const handleConfirmClick = async (e) => {
    var _a, _b, _c, _d;
    e.preventDefault();
    if (!formData.terms_agreed) {
      toast.error(
        translate(
          "sell.conditions_terms",
          "Please read and agree to our terms and conditions"
        )
      );
      return;
    }
    try {
      const { data } = await axios.post(
        route("api.tickets.store"),
        formData
      );
      if (data.success) {
        toast.success(data.message);
        router.visit(route("dashboard"));
      }
    } catch (error) {
      if (((_a = error.response) == null ? void 0 : _a.status) === 422 && ((_b = error.response.data) == null ? void 0 : _b.errors)) {
        toast.error((_d = Object.values((_c = error.response.data) == null ? void 0 : _c.errors)[0]) == null ? void 0 : _d[0]);
      } else {
        toast.error(translate("common.something_wrong"));
      }
    }
  };
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold mb-3", children: translate("sell.step_4_title") }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.quantity"),
          " :"
        ] }),
        " ",
        formData.quantity
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.price"),
          " :"
        ] }),
        " ",
        formData.price
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.face_value_price"),
          " :"
        ] }),
        " ",
        formData.face_value_price
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.sector_id"),
          " :"
        ] }),
        " ",
        formData.sector_name
      ] }),
      formData.ticket_rows !== "" && /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.ticket_rows"),
          " :"
        ] }),
        " ",
        formData.ticket_rows
      ] }),
      formData.ticket_seats !== "" && /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.ticket_seats"),
          " :"
        ] }),
        " ",
        formData.ticket_seats
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.ticket_type"),
          " :"
        ] }),
        " ",
        translate(
          `enums.ticket_types.${formData.ticket_type}`
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxs("span", { className: "font-bold", children: [
          translate("sell.labels.quantity_split_type"),
          " :"
        ] }),
        " ",
        translate(
          `enums.ticket_quantity_split_types.${formData.quantity_split_type}`
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "divider" }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center text-sm text-gray-700 cursor-pointer", children: [
        /* @__PURE__ */ jsx(
          "input",
          {
            type: "checkbox",
            className: "rounded border-gray-300 text-indigo-600 focus:ring-indigo-500",
            checked: formData.terms_agreed,
            onChange: (e) => updateFormData("terms_agreed", e.target.checked)
          }
        ),
        /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
          translate(
            "sell.read_agree_text",
            "I have read and agree to the"
          ),
          " ",
          /* @__PURE__ */ jsx("a", { href: "#", className: "link link-primary", children: translate(
            "sell.terms_conditions_text",
            "terms & conditions"
          ) })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between mt-4", children: [
      /* @__PURE__ */ jsx("button", { className: "btn btn-outline", onClick: prevStep, children: translate("sell.back_btn", "Back") }),
      /* @__PURE__ */ jsx(
        "button",
        {
          className: "btn btn-primary",
          onClick: handleConfirmClick,
          children: translate("sell.confirm_create_btn")
        }
      )
    ] })
  ] }) });
}
function TicketFormSection() {
  const { translate } = useTranslations();
  const { step } = useTicketSell();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs("ul", { className: "steps mb-5 w-full", children: [
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 1 ? "step-primary" : ""}`, children: translate("sell.step_1_title") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 2 ? "step-primary" : ""}`, children: translate("sell.step_2_title") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 3 ? "step-primary" : ""}`, children: translate("sell.step_3_title") }),
      /* @__PURE__ */ jsx("li", { className: `step ${step >= 4 ? "step-primary" : ""}`, children: translate("sell.step_4_title") })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "divider mt-0 mb-2" }),
    step === 1 && /* @__PURE__ */ jsx(QuantityPriceStep, {}),
    step === 2 && /* @__PURE__ */ jsx(TypeDetailStep, {}),
    step === 3 && /* @__PURE__ */ jsx(RestrictionsStep, {}),
    step === 4 && /* @__PURE__ */ jsx(ReviewStep, {})
  ] });
}
function Sell() {
  const { slug } = usePage().props;
  const { translate } = useTranslations();
  const {
    event,
    eventLoading,
    configurations,
    getEventDetail,
    getConfigurationsData
  } = useTicketSell();
  useEffect(() => {
    getConfigurationsData();
    getEventDetail(slug);
  }, []);
  if (eventLoading) {
    return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx("div", { className: "p-8 flex items-center justify-center h-96", children: /* @__PURE__ */ jsx("span", { className: "loading loading-bars loading-xl" }) }) });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("sell.head_title") }),
    /* @__PURE__ */ jsx("div", { className: "container mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:flex-row gap-8 items-start px-6 py-5", children: [
      /* @__PURE__ */ jsx("div", { className: "w-full md:w-2/5 bg-base-100 rounded-xl shadow", children: /* @__PURE__ */ jsx(EventDetailSection, { event }) }),
      /* @__PURE__ */ jsx("div", { className: "w-full md:w-3/5 bg-base-100 rounded-xl shadow p-5", children: configurations.can_create_ticket ? /* @__PURE__ */ jsx(TicketFormSection, {}) : /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm", children: [
        /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsx(AlertTriangle, { className: "w-12 h-12" }) }),
        /* @__PURE__ */ jsx("h2", { className: "text-2xl font-semibold text-base-content", children: translate("sell.limit_exceed_text") }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-500 mt-2 max-w-md text-center", children: /* @__PURE__ */ jsx(
          "a",
          {
            className: "underline underline-offset-4",
            href: "mailto:<EMAIL>",
            children: translate("sell.become_seller_text")
          }
        ) })
      ] }) })
    ] }) })
  ] });
}
Sell.layout = (page) => /* @__PURE__ */ jsx(AppLayout$1, { children: page });
const __vite_glob_0_32 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Sell
}, Symbol.toStringTag, { value: "Module" }));
function UnderConstruction() {
  const { translate } = useTranslations();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Head, { title: translate("common.under_construction_text") }),
    /* @__PURE__ */ jsx("div", { className: "min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-6xl font-bold text-white mb-4", children: translate("common.under_construction_text") }),
      /* @__PURE__ */ jsx("p", { className: "text-xl text-white mb-8", children: translate("common.working_hard_text") }),
      /* @__PURE__ */ jsx("div", { className: "animate-bounce", children: /* @__PURE__ */ jsx(
        "svg",
        {
          className: "w-16 h-16 text-yellow-300 mx-auto",
          fill: "none",
          strokeLinecap: "round",
          strokeLinejoin: "round",
          strokeWidth: "2",
          viewBox: "0 0 24 24",
          stroke: "currentColor",
          children: /* @__PURE__ */ jsx("path", { d: "M19 14l-7 7m0 0l-7-7m7 7V3" })
        }
      ) }),
      /* @__PURE__ */ jsx("p", { className: "text-white mt-8", children: translate("common.comming_soon_text") })
    ] }) })
  ] });
}
UnderConstruction.layout = (page) => /* @__PURE__ */ jsx(AppLayout, { children: page });
const __vite_glob_0_33 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: UnderConstruction
}, Symbol.toStringTag, { value: "Module" }));
axios$1.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";
if (typeof document !== "undefined") {
  const htmlLang = document.documentElement.lang;
  axios$1.defaults.headers.common["X-Locale"] = htmlLang;
}
axios$1.defaults.withCredentials = true;
axios$1.defaults.withXSRFToken = true;
axios$1.interceptors.response.use((response) => {
  if (response.data.success && response.data.data) {
    response.data = {
      ...response.data.data,
      success: response.data.success,
      message: response.data.message
    };
    return response;
  }
  return response;
});
const store = configureStore({
  reducer: {
    events: eventsReducer,
    event: eventDetailReducer,
    stadiums: stadiumsReducer,
    stadium: stadiumDetailReducer,
    clubs: clubsReducer,
    club: clubDetailReducer,
    leagues: leaguesReducer,
    league: leagueDetailReducer,
    ticketCheckout: ticketCheckoutReducer,
    sellTickets: sellTicketsReducer,
    ticketSell: ticketSellReducer,
    editTicket: editTicketReducer,
    translations: translationReducer,
    orders: ordersReducer,
    myTickets: myTicketsReducer
  }
});
const appName = "TicketGol";
Sentry.init({
  dsn: "https://<EMAIL>/4509332236075088",
  sendDefaultPii: true,
  integrations: [Sentry.replayIntegration()],
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1
});
createServer(
  (page) => createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    page,
    render: ReactDOMServer.renderToString,
    resolve: (name) => {
      const pages = /* @__PURE__ */ Object.assign({ "./Pages/Auth/ConfirmPassword.jsx": __vite_glob_0_0, "./Pages/Auth/ForgotPassword.jsx": __vite_glob_0_1, "./Pages/Auth/Login.jsx": __vite_glob_0_2, "./Pages/Auth/Register.jsx": __vite_glob_0_3, "./Pages/Auth/ResetPassword.jsx": __vite_glob_0_4, "./Pages/Auth/VerifyEmail.jsx": __vite_glob_0_5, "./Pages/CMSPage.jsx": __vite_glob_0_6, "./Pages/Checkout/Cancel.jsx": __vite_glob_0_7, "./Pages/Checkout/Success.jsx": __vite_glob_0_8, "./Pages/Clubs/Index.jsx": __vite_glob_0_9, "./Pages/Clubs/Show.jsx": __vite_glob_0_10, "./Pages/Dashboard.jsx": __vite_glob_0_11, "./Pages/Error.jsx": __vite_glob_0_12, "./Pages/Events/Index.jsx": __vite_glob_0_13, "./Pages/Events/SellTickets.jsx": __vite_glob_0_14, "./Pages/Events/Show.jsx": __vite_glob_0_15, "./Pages/Home.jsx": __vite_glob_0_16, "./Pages/Leagues/Index.jsx": __vite_glob_0_17, "./Pages/Leagues/Show.jsx": __vite_glob_0_18, "./Pages/MyAccount/Dashboard.jsx": __vite_glob_0_19, "./Pages/MyAccount/OrderDetail.jsx": __vite_glob_0_20, "./Pages/MyAccount/Orders.jsx": __vite_glob_0_21, "./Pages/MyAccount/Profile/Edit.jsx": __vite_glob_0_22, "./Pages/MyAccount/Profile/Partials/DeleteUserForm.jsx": __vite_glob_0_23, "./Pages/MyAccount/Profile/Partials/UpdatePasswordForm.jsx": __vite_glob_0_24, "./Pages/MyAccount/Profile/Partials/UpdateProfileInformationForm.jsx": __vite_glob_0_25, "./Pages/MyAccount/Tickets/Edit.jsx": __vite_glob_0_26, "./Pages/MyAccount/Tickets/Index.jsx": __vite_glob_0_27, "./Pages/Search.jsx": __vite_glob_0_28, "./Pages/Stadiums/Index.jsx": __vite_glob_0_29, "./Pages/Stadiums/Show.jsx": __vite_glob_0_30, "./Pages/Ticket/Checkout.jsx": __vite_glob_0_31, "./Pages/Ticket/Sell.jsx": __vite_glob_0_32, "./Pages/UnderConstruction.jsx": __vite_glob_0_33 });
      return pages[`./Pages/${name}.jsx`];
    },
    setup({ el, App, props }) {
      const root = hydrateRoot(el);
      root.render(
        /* @__PURE__ */ jsx(Provider, { store, children: /* @__PURE__ */ jsx(App, { ...props }) })
      );
    }
  })
);
//# sourceMappingURL=ssr.js.map
